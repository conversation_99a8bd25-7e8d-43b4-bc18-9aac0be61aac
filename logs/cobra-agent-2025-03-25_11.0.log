11:08:06.908 [main] [] INFO  o.a.d.common.logger.LoggerFactory - using logger: org.apache.dubbo.common.logger.log4j.Log4jLoggerAdapter
11:08:06.918 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

11:08:06.924 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.apidoc.contextPath=/cobra-agent/dubbo, dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.protocols.whttp.dispatcher=all, dubbo.protocols.whttp.name=whttp, dubbo.protocols.whttp.port=9090, dubbo.protocols.whttp.server=wservlet, dubbo.protocols.whttp.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.provider.version=3.0.0, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.cobra.agent.spi.service,com.winit.gateway.dubbo.swagger.service}
11:08:06.940 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
11:08:07.034 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 70319 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
11:08:07.034 [main] [] INFO  c.w.c.agent.CobraAgentApplication - The following profiles are active: dev
11:08:07.797 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
11:08:07.797 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
11:08:07.797 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
11:08:07.798 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
11:08:07.798 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
11:08:08.008 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
11:08:08.009 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
11:08:08.033 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
11:08:08.115 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:08.115 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
11:08:08.115 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:08.115 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:08.115 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:08.115 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:08.115 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : whttp, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:08.120 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
11:08:08.167 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
11:08:08.174 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:08.174 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:08.184 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:com.winit.cobra.agent.spi.service.ChatService:3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:08.184 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:com.winit.cobra.agent.spi.service.ConversationService:3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:08.184 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] 2 annotated Dubbo's @DubboService Components { [Bean definition with name 'chatService': Generic bean: class [com.winit.cobra.agent.service.ChatServiceImpl]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes/com/winit/cobra/agent/service/ChatServiceImpl.class], Bean definition with name 'conversationService': Generic bean: class [com.winit.cobra.agent.service.ConversationServiceImpl]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes/com/winit/cobra/agent/service/ConversationServiceImpl.class]] } were scanned under package[com.winit.cobra.agent], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:08.195 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
11:08:08.763 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
11:08:08.769 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
11:08:08.770 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
11:08:08.770 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
11:08:08.827 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
11:08:08.827 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1752 ms
11:08:09.322 [main] [] INFO  org.redisson.Version - Redisson 3.20.1
11:08:09.352 [main] [] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:64)
	at org.redisson.connection.ServiceManager.<init>(ServiceManager.java:162)
	at org.redisson.config.ConfigSupport.createConnectionManager(ConfigSupport.java:184)
	at org.redisson.Redisson.<init>(Redisson.java:69)
	at org.redisson.Redisson.create(Redisson.java:114)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration.redisson(WinitRedissonAutoConfiguration.java:191)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration$$EnhancerBySpringCGLIB$$bdd187fe.CGLIB$redisson$0(<generated>)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration$$EnhancerBySpringCGLIB$$bdd187fe$$FastClassBySpringCGLIB$$b625a284.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration$$EnhancerBySpringCGLIB$$bdd187fe.redisson(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:521)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.winit.cobra.agent.CobraAgentApplication.main(CobraAgentApplication.java:14)
Caused by: java.lang.UnsatisfiedLinkError: 'io.netty.resolver.dns.macos.DnsResolver[] io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.resolvers()'
	at io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.resolvers(Native Method)
	at io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.retrieveCurrentMappings(MacOSDnsServerAddressStreamProvider.java:127)
	at io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.<init>(MacOSDnsServerAddressStreamProvider.java:123)
	... 114 common frames omitted
11:08:09.439 [main] [] INFO  o.r.cluster.ClusterConnectionManager - Redis cluster nodes configuration got from ************/************:6380:
8fc5aef94cb00c43b94b4e70d357932efbab5804 ************:6379@16379 master - 0 1742872077273 8 connected 5462-10922
fade1bcb8d0ac21d3fa1ba8946e00f5e2453be98 ************:6380@16380 slave 01d5d53c96d71e836896b141a1b6789b891a41c9 0 1742872079286 12 connected
01d5d53c96d71e836896b141a1b6789b891a41c9 ************:6379@16379 master - 0 1742872078000 12 connected 0-5461
c6b554c1d8278a109d8ce273c017c81bc533820f ************:6379@16379 slave a7aadadc1d5397d8b191d7a873eb9f8a80bc604c 0 1742872079000 11 connected
f83dc570be6f83a2efaf9d0cdaa0052152f87bd8 ************:6380@16380 slave 8fc5aef94cb00c43b94b4e70d357932efbab5804 0 1742872079000 8 connected
a7aadadc1d5397d8b191d7a873eb9f8a80bc604c ************:6380@16380 myself,master - 0 1742872077000 11 connected 10923-16383

11:08:09.469 [redisson-netty-2-16] [] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ************/************:6380
11:08:09.482 [redisson-netty-2-23] [] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ************/************:6379
11:08:09.487 [redisson-netty-2-1] [] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ************/************:6379
11:08:09.774 [redisson-netty-2-23] [] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ************/************:6380
11:08:09.781 [redisson-netty-2-27] [] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ************/************:6379
11:08:09.781 [redisson-netty-2-5] [] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ************/************:6379
11:08:09.796 [redisson-netty-2-32] [] INFO  o.r.c.pool.PubSubConnectionPool - 1 connections initialized for ************/************:6379
11:08:09.809 [redisson-netty-2-17] [] INFO  o.r.c.pool.PubSubConnectionPool - 1 connections initialized for ************/************:6380
11:08:09.809 [redisson-netty-2-22] [] INFO  o.r.c.pool.PubSubConnectionPool - 1 connections initialized for ************/************:6380
11:08:10.090 [redisson-netty-2-16] [] INFO  o.r.cluster.ClusterConnectionManager - slaves: [redis://************:6379] added for slot ranges: [[10923-16383]]
11:08:10.091 [redisson-netty-2-16] [] INFO  o.r.cluster.ClusterConnectionManager - master: redis://************:6380 added for slot ranges: [[10923-16383]]
11:08:10.091 [redisson-netty-2-16] [] INFO  o.r.c.pool.SlaveConnectionPool - 24 connections initialized for ************/************:6379
11:08:10.104 [redisson-netty-2-21] [] INFO  o.r.cluster.ClusterConnectionManager - slaves: [redis://************:6380] added for slot ranges: [[5462-10922]]
11:08:10.104 [redisson-netty-2-22] [] INFO  o.r.cluster.ClusterConnectionManager - slaves: [redis://************:6380] added for slot ranges: [[0-5461]]
11:08:10.104 [redisson-netty-2-21] [] INFO  o.r.cluster.ClusterConnectionManager - master: redis://************:6379 added for slot ranges: [[5462-10922]]
11:08:10.104 [redisson-netty-2-22] [] INFO  o.r.cluster.ClusterConnectionManager - master: redis://************:6379 added for slot ranges: [[0-5461]]
11:08:10.104 [redisson-netty-2-21] [] INFO  o.r.c.pool.SlaveConnectionPool - 24 connections initialized for ************/************:6380
11:08:10.104 [redisson-netty-2-22] [] INFO  o.r.c.pool.SlaveConnectionPool - 24 connections initialized for ************/************:6380
11:08:10.221 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
11:08:10.227 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
11:08:10.232 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
11:08:10.234 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
11:08:10.235 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='whttp', host='null', port=9090, contextpath='null', threadpool='null', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='wservlet', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=whttp, port=9090, threads=200, dispatcher=all, server=wservlet}]
11:08:10.239 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random, version=3.0.0}]
11:08:10.246 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
11:08:10.247 [main] [] INFO  o.a.d.r.c.r.ubarrier.DubboSwitcher -  [DUBBO] dubbo已切换到GNONE模式., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.250 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
11:08:10.254 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : zookeeper] supports as the config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.254 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] will be used as the config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.285 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
11:08:10.297 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
11:08:10.301 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/com/winit/uschedule/uschedule-autoconfig-support/1.9.5-SNAPSHOT/uschedule-autoconfig-support-1.9.5-20210729.082348-11.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/com/winit/uschedule/uschedule-task-tracker/1.9.5-SNAPSHOT/uschedule-task-tracker-1.9.5-20210729.082336-11.jar:/Users/<USER>/.m2/repository/com/winit/uschedule/uschedule-core/1.9.5-SNAPSHOT/uschedule-core-1.9.5-20210729.082334-11.jar:/Users/<USER>/.m2/repository/commons-dbutils/commons-dbutils/1.6/commons-dbutils-1.6.jar:/Users/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.30/log4j-over-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz/2.3.2/quartz-2.3.2.jar:/Users/<USER>/.m2/repository/com/mchange/mchange-commons-java/0.2.15/mchange-commons-java-0.2.15.jar:/Users/<USER>/.m2/repository/com/101tec/zkclient/0.4/zkclient-0.4.jar:/Users/<USER>/.m2/repository/io/github/tronprotocol/leveldbjni-all/1.18.3/leveldbjni-all-1.18.3.jar:/Users/<USER>/.m2/repository/org/fusesource/hawtjni/hawtjni-runtime/1.18/hawtjni-runtime-1.18.jar:/Users/<USER>/.m2/repository/com/halibobor/leveldb-api/1.18.3/leveldb-api-1.18.3.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/com/winit/distributed-lock-boot-starter/1.0.2-SNAPSHOT/distributed-lock-boot-starter-1.0.2-20230816.024233-2.jar:/Users/<USER>/.m2/repository/com/winit/distributed-lock/1.0.2-SNAPSHOT/distributed-lock-1.0.2-20230816.024231-2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.20.1/redisson-3.20.1.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.65.Final/netty-common-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.65.Final/netty-codec-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.65.Final/netty-buffer-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.65.Final/netty-transport-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.65.Final/netty-resolver-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.65.Final/netty-resolver-dns-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.65.Final/netty-codec-dns-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.65.Final/netty-handler-4.1.65.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.6/rxjava-3.1.6.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jboss-marshalling/2.0.11.Final/jboss-marshalling-2.0.11.Final.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jboss-marshalling-river/2.0.11.Final/jboss-marshalling-river-2.0.11.Final.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.4.0/kryo-5.4.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-bean/5.1.6/jodd-bean-5.1.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-core/5.1.6/jodd-core-5.1.6.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.17.5/redisson-spring-boot-starter-3.17.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.4.6/spring-boot-starter-data-redis-2.4.6.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-27/3.17.5/redisson-spring-data-27-3.17.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.9/mybatis-plus-jsqlparser-3.5.9.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.0/jsqlparser-5.0.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.9/mybatis-plus-jsqlparser-common-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-common/target/classes:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.1/objenesis-3.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.3.1-SNAPSHOT/gateway-apache-dubbo-1.3.1-20231213.032957-1.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.3.1-SNAPSHOT/gateway-common-1.3.1-20231213.032935-1.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-webflux/2.4.6/spring-boot-starter-webflux-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/2.4.6/spring-boot-starter-reactor-netty-2.4.6.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.0.7/reactor-netty-http-1.0.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.65.Final/netty-codec-http-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.65.Final/netty-codec-http2-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.65.Final/netty-resolver-dns-native-macos-4.1.65.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.65.Final/netty-transport-native-unix-common-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.65.Final/netty-transport-native-epoll-4.1.65.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.0.7/reactor-netty-core-1.0.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.65.Final/netty-handler-proxy-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.65.Final/netty-codec-socks-4.1.65.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/5.3.7/spring-webflux-5.3.7.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.3.2
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
11:08:10.302 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@203ba07
11:08:10.310 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
11:08:10.311 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
11:08:10.314 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
11:08:10.325 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x195b1aee7c701bd, negotiated timeout = 60000
11:08:10.328 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
11:08:10.328 [main] [] INFO  o.a.d.r.z.ZookeeperTransporter -  [DUBBO] No valid zookeeper client found from cache, therefore create a new client for url. zookeeper://************:2181/ConfigCenterConfig?check=true&config-file=dubbo.properties&group=dubbo&highest-priority=false&timeout=3000, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.331 [Curator-ConnectionStateManager-0] [] INFO  o.a.d.r.z.c.CuratorZookeeperClient -  [DUBBO] Curator zookeeper client instance initiated successfully, session id is 195b1aee7c701bd, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.353 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No global configuration in config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.353 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No application level configuration in config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.365 [main] [] INFO  o.a.d.c.utils.ConfigValidationUtils -  [DUBBO] No valid monitor config found, specify monitor info to enable collection of Dubbo statistics, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.372 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : zookeeper] supports as the metadata center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.372 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] will be used as the metadata center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.384 [main] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] Load service store file /Users/<USER>/.dubbo/dubbo-metadata-cobra-agent-************-2181.cache, data: {com.winit.ums.spi.UmsUserService:3.0.0::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken","dubbo":"2.0.2","check":"false","interface":"com.winit.ums.spi.UmsUserService","version":"3.0.0","qos.enable":"false","revision":"1.2.197-SNAPSHOT","protocol":"dubbo","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.cobra.service.ChatService:::provider:cobra-agent={"annotations":[],"canonicalName":"com.winit.cobra.service.ChatService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/","methods":[{"annotations":[],"name":"chat","parameterTypes":["com.winit.cobra.dto.request.ChatCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.ChatVo"},{"annotations":[],"name":"streamChat","parameterTypes":["com.winit.cobra.dto.request.StreamChatCommand"],"parameters":[],"returnType":"reactor.core.publisher.Flux"},{"annotations":[],"name":"listChat","parameterTypes":["com.winit.cobra.dto.request.ListChatCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.ChatMessagesVo"},{"annotations":[],"name":"listLatestChat","parameterTypes":["com.winit.cobra.dto.request.ListLatestChatCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.LastedChatMessagesVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"chat,streamChat,listChat,listLatestChat","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.service.ChatService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.service.ChatService","threads":"200","generic":"false","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enums":[],"items":[],"properties":{"chatMessages":{"enums":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.dto.response.ChatMessageVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"},"conversationId":{"enums":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"com.winit.cobra.dto.response.LastedChatMessagesVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"chatMessages":{"enums":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.dto.response.ChatMessageVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.dto.response.ChatMessagesVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"month":{"enums":[],"items":[],"properties":{},"type":"short","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"year":{"enums":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"day":{"$ref":"$.types[2].properties.month"}},"type":"java.time.LocalDate","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{},"type":"reactor.core.publisher.Flux","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{},"type":"byte","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.conversationId"},"message":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"},"username":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.request.StreamChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"role":{"$ref":"$.types[0].properties.conversationId"},"content":{"$ref":"$.types[0].properties.conversationId"},"timestamp":{"enums":[],"items":[],"properties":{"date":{"$ref":"$.types[2]"},"time":{"enums":[],"items":[],"properties":{"hour":{"$ref":"$.types[4]"},"nano":{"$ref":"$.types[2].properties.year"},"minute":{"$ref":"$.types[4]"},"second":{"$ref":"$.types[4]"}},"type":"java.time.LocalTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"java.time.LocalDateTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"com.winit.cobra.dto.response.ChatMessageVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[6].properties.timestamp"},{"enums":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.conversationId"},"message":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"},"username":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.request.ChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"},"topN":{"$ref":"$.types[2].properties.year"}},"type":"com.winit.cobra.dto.request.ListChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.month"},{"$ref":"$.types[0].properties.conversationId"},{"enums":[],"items":[],"properties":{"conversionId":{"$ref":"$.types[0].properties.conversationId"},"content":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.response.ChatVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.year"},{"$ref":"$.types[6].properties.timestamp.properties.time"},{"enums":[],"items":[],"properties":{"userId":{"$ref":"$.types[0].properties.conversationId"},"topN":{"$ref":"$.types[2].properties.year"}},"type":"com.winit.cobra.dto.request.ListLatestChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.service.ChatService@file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/"}, com.winit.cobra.service.ConversationService:::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"createConversation,listConversations","injvm":"true","dubbo":"2.0.2","check":"false","interface":"com.winit.cobra.service.ConversationService","qos.enable":"false","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.cobra.service.ChatService:::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"chat,streamChat,listChat,listLatestChat","injvm":"true","dubbo":"2.0.2","check":"false","interface":"com.winit.cobra.service.ChatService","qos.enable":"false","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.ums.spi.UmsUserService:::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken","dubbo":"2.0.2","check":"false","interface":"com.winit.ums.spi.UmsUserService","qos.enable":"false","revision":"1.2.197-SNAPSHOT","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.cobra.agent.spi.service.ConversationService:3.0.0::provider:cobra-agent={"annotations":["@io.swagger.annotations.ApiModel(reference=\"\", parent=java.lang.Void.class, description=\"\\u4f1a\\u8bdd\\u7ba1\\u7406\", subTypes={}, value=\"\", discriminator=\"\")"],"canonicalName":"com.winit.cobra.agent.spi.service.ConversationService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/","methods":[{"annotations":[],"name":"createConversation","parameterTypes":["com.winit.cobra.agent.spi.dto.request.CreateConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.CreateConversationVo"},{"annotations":[],"name":"listConversations","parameterTypes":["com.winit.cobra.agent.spi.dto.request.ListConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.PageConversationsVo"},{"annotations":[],"name":"startConversation","parameterTypes":["com.winit.cobra.agent.spi.dto.request.StartConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.StartConversationVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"startConversation,createConversation,listConversations","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.agent.spi.service.ConversationService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0","threads":"200","version":"3.0.0","generic":"false","revision":"3.0.0","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enum":[],"items":[],"properties":{"created":{"enum":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"conversationId":{"$ref":"$.types[0].properties.created"},"customerCode":{"$ref":"$.types[0].properties.created"},"active":{"enum":[],"items":[],"properties":{},"type":"boolean","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"userId":{"$ref":"$.types[0].properties.created"},"updated":{"$ref":"$.types[0].properties.created"},"customerName":{"$ref":"$.types[0].properties.created"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.response.ListConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"requestUrl":{"$ref":"$.types[0].properties.created"},"attributes":{"enum":[],"items":[{"$ref":"$.types[0].properties.created"},{"enum":[],"items":[],"properties":{},"type":"java.lang.Object","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"ipList":{"enum":[],"items":[],"properties":{},"type":"java.util.Collection<java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.common.spi.context.CommandContext","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"ctx":{"$ref":"$.types[2]"},"userId":{"$ref":"$.types[0].properties.created"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.request.CreateConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"long","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"startNewConversation":{"$ref":"$.types[0].properties.active"},"ctx":{"$ref":"$.types[2]"},"customerCode":{"$ref":"$.types[0].properties.created"},"userId":{"$ref":"$.types[0].properties.created"},"customerName":{"$ref":"$.types[0].properties.created"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.request.StartConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.response.StartConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.attributes.items[1]"},{"$ref":"$.types[0].properties.active"},{"enum":[],"items":[],"properties":{"total":{"$ref":"$.types[4]"},"conversationVos":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.response.ListConversationVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.agent.spi.dto.response.PageConversationsVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[0].properties.created"},{"enum":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.created"},"pageNo":{"$ref":"$.types[1]"},"ctx":{"$ref":"$.types[2]"},"customerCode":{"$ref":"$.types[0].properties.created"},"startTime":{"$ref":"$.types[0].properties.created"},"endTime":{"$ref":"$.types[0].properties.created"},"userId":{"$ref":"$.types[0].properties.created"},"customerName":{"$ref":"$.types[0].properties.created"},"topN":{"$ref":"$.types[1]"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.request.ListConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"chatId":{"$ref":"$.types[0].properties.created"},"conversationId":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.response.CreateConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.agent.spi.service.ConversationService@file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/"}, com.winit.cobra.agent.spi.service.ChatService:3.0.0::provider:cobra-agent={"annotations":["@io.swagger.annotations.ApiModel(reference=\"\", parent=java.lang.Void.class, description=\"\\u804a\\u5929\\u670d\\u52a1\", subTypes={}, value=\"\", discriminator=\"\")"],"canonicalName":"com.winit.cobra.agent.spi.service.ChatService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/","methods":[{"annotations":[],"name":"chat","parameterTypes":["com.winit.cobra.agent.spi.dto.request.ChatCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.ChatVo"},{"annotations":[],"name":"streamChat","parameterTypes":["com.winit.cobra.agent.spi.dto.request.StreamChatCommand"],"parameters":[],"returnType":"reactor.core.publisher.Flux"},{"annotations":[],"name":"listChat","parameterTypes":["com.winit.cobra.agent.spi.dto.request.ListChatCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo"},{"annotations":[],"name":"transferToHuman","parameterTypes":["com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.TransferToHumanVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"transferToHuman,chat,streamChat,listChat","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.agent.spi.service.ChatService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0","threads":"200","version":"3.0.0","generic":"false","revision":"3.0.0","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enum":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"requestUrl":{"enum":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"attributes":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"enum":[],"items":[],"properties":{},"type":"java.lang.Object","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"ipList":{"enum":[],"items":[],"properties":{},"type":"java.util.Collection<java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.common.spi.context.CommandContext","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"toolContext":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.attributes.items[1]"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"ctx":{"$ref":"$.types[1]"},"customerCode":{"$ref":"$.types[1].properties.requestUrl"},"message":{"$ref":"$.types[1].properties.requestUrl"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"customerName":{"$ref":"$.types[1].properties.requestUrl"},"customVariables":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"metaData":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"autoSaveHistory":{"enum":[],"items":[],"properties":{},"type":"boolean","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"toolCallOut":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.request.SubmitToolCallCommand>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.StreamChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"metaData":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"autoSaveHistory":{"$ref":"$.types[2].properties.autoSaveHistory"},"toolContext":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.attributes.items[1]"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"ctx":{"$ref":"$.types[1]"},"toolCallOut":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.request.SubmitToolCallCommand>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"},"message":{"$ref":"$.types[1].properties.requestUrl"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"customVariables":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.ChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"long","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.attributes.items[1]"},{"enum":[],"items":[],"properties":{},"type":"java.lang.Boolean","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"createdTimeDesc":{"$ref":"$.types[2].properties.autoSaveHistory"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"pageNo":{"$ref":"$.types[0]"},"ctx":{"$ref":"$.types[1]"},"startTime":{"$ref":"$.types[1].properties.requestUrl"},"endTime":{"$ref":"$.types[1].properties.requestUrl"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"topN":{"$ref":"$.types[0]"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.ListChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.autoSaveHistory"},{"enum":[],"items":[],"properties":{"humanAgentType":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"ctx":{"$ref":"$.types[1]"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"transferIdentity":{"$ref":"$.types[1].properties.requestUrl"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.requestUrl"},{"enum":[],"items":[],"properties":{"total":{"$ref":"$.types[4]"},"chatMessages":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.response.ChatMessageVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"reactor.core.publisher.Flux","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"logId":{"$ref":"$.types[1].properties.requestUrl"},"content":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.response.ChatVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"output":{"$ref":"$.types[1].properties.requestUrl"},"toolCallId":{"$ref":"$.types[1].properties.requestUrl"},"functionName":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.SubmitToolCallCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"com.winit.cobra.agent.spi.dto.response.TransferToHumanVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"role":{"$ref":"$.types[1].properties.requestUrl"},"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"likeOrDislike":{"$ref":"$.types[6]"},"debugContent":{"$ref":"$.types[1].properties.requestUrl"},"content":{"$ref":"$.types[1].properties.requestUrl"},"feedbackInfo":{"$ref":"$.types[1].properties.requestUrl"},"timestamp":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.response.ChatMessageVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.agent.spi.service.ChatService@file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/"}, com.winit.cobra.service.ConversationService:::provider:cobra-agent={"annotations":[],"canonicalName":"com.winit.cobra.service.ConversationService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/","methods":[{"annotations":[],"name":"createConversation","parameterTypes":["com.winit.cobra.dto.request.CreateConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.CreateConversationVo"},{"annotations":[],"name":"listConversations","parameterTypes":["com.winit.cobra.dto.request.ListConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.ListConversationsVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"createConversation,listConversations","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.service.ConversationService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.service.ConversationService","threads":"200","generic":"false","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enums":[],"items":[],"properties":{"conversationId":{"enums":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"com.winit.cobra.dto.response.CreateConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"date":{"enums":[],"items":[],"properties":{"month":{"enums":[],"items":[],"properties":{},"type":"short","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"year":{"enums":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"day":{"$ref":"$.types[1].properties.date.properties.month"}},"type":"java.time.LocalDate","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"time":{"enums":[],"items":[],"properties":{"hour":{"enums":[],"items":[],"properties":{},"type":"byte","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"nano":{"$ref":"$.types[1].properties.date.properties.year"},"minute":{"$ref":"$.types[1].properties.time.properties.hour"},"second":{"$ref":"$.types[1].properties.time.properties.hour"}},"type":"java.time.LocalTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"java.time.LocalDateTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"userId":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.request.CreateConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"userId":{"$ref":"$.types[0].properties.conversationId"},"topN":{"$ref":"$.types[1].properties.date.properties.year"}},"type":"com.winit.cobra.dto.request.ListConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.date.properties.month"},{"$ref":"$.types[0].properties.conversationId"},{"$ref":"$.types[1].properties.date"},{"enums":[],"items":[],"properties":{"conversationVos":{"enums":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.dto.response.ListConversationVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.dto.response.ListConversationsVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.date.properties.year"},{"$ref":"$.types[1].properties.time"},{"$ref":"$.types[1].properties.time.properties.hour"},{"enums":[],"items":[],"properties":{"lastUpdated":{"$ref":"$.types[1]"},"conversationId":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.response.ListConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.service.ConversationService@file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/"}}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.401 [main] [] INFO  o.a.d.r.z.ZookeeperTransporter -  [DUBBO] find valid zookeeper client from the cache for address: zookeeper://************:2181/org.apache.dubbo.metadata.report.MetadataReport?application=cobra-agent&client=&port=2181&protocol=zookeeper, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.404 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.460 [main] [] INFO  o.a.d.q.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.462 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Load registry cache file /Users/<USER>/.dubbo/dubbo-registry-cobra-agent-************-2181.cache, data: {com.winit.cobra.agent.spi.service.ConversationService:3.0.0=empty://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=8922&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742815367872&validation=true&version=3.0.0, com.winit.cobra.agent.spi.service.ChatService:3.0.0=empty://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=8922&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742815367937&validation=true&version=3.0.0, com.winit.cobra.service.ConversationService=empty://************:20812/com.winit.cobra.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=************&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.service.ConversationService&loadbalance=random&metadata-type=remote&methods=createConversation,listConversations&organization=winit&owner=winit&pid=94870&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&service.name=ServiceBean:/com.winit.cobra.service.ConversationService&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1736505562109&validation=true, com.winit.cobra.service.ChatService=empty://************:20812/com.winit.cobra.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=************&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.service.ChatService&loadbalance=random&metadata-type=remote&methods=chat,streamChat,listChat,listLatestChat&organization=winit&owner=winit&pid=94870&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&service.name=ServiceBean:/com.winit.cobra.service.ChatService&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&validation=true, com.winit.ums.spi.UmsUserService:3.0.0=empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=8922&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0 empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=configurators&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=8922&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0 dubbo://************:2488/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0 whttp://************:2480/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&server=servlet&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0, com.winit.ums.spi.UmsUserService=empty://************/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=83619&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=************* empty://************/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=configurators&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=83619&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=************* empty://************/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=83619&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.462 [main] [] INFO  o.a.d.r.z.ZookeeperTransporter -  [DUBBO] find valid zookeeper client from the cache for address: zookeeper://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&interface=org.apache.dubbo.registry.RegistryService&organization=winit&owner=winit&pid=70319&qos.enable=false&release=2.7.16-SNAPSHOT&timestamp=*************, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.467 [main] [] INFO  o.a.d.r.c.m.MigrationRuleListener -  [DUBBO] Listening for migration rules on dataId-cobra-agent.migration group-MIGRATION, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.472 [main] [] INFO  o.a.d.r.c.m.MigrationRuleListener -  [DUBBO] Using the following migration rule to migrate:, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.472 [main] [] INFO  o.a.d.r.c.m.MigrationRuleListener -  [DUBBO] INIT, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.497 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Register: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=consumers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.528 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Subscribe: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.579 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Notify urls for subscribe url consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, urls: [dubbo://************:2488/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0, whttp://************:2480/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&server=servlet&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0, empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=configurators&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.630 [NettyClientWorker-5-1] [] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /127.0.0.1:56969 -> /127.0.0.1:7897 is established., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.631 [main] [] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Successed connect to server /127.0.0.1:7897 from NettyClient *********** using dubbo version 2.7.16-SNAPSHOT, channel is NettyChannel [channel=[id: 0xb55fbf14, L:/127.0.0.1:56969 - R:/127.0.0.1:7897]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.631 [main] [] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Start NettyClient /*********** connect to the server /127.0.0.1:7897, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.653 [main] [] INFO  o.a.dubbo.config.ReferenceConfig -  [DUBBO] Refer dubbo service com.winit.ums.spi.UmsUserService from url dubbo://***********/com.winit.ums.spi.UmsUserService?anyhost=true&application=cobra-agent&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&init=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&register.ip=***********&release=2.7.16-SNAPSHOT&remote.application=ums&retries=2&revision=1.2.197-SNAPSHOT&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=consumer&sticky=false&timeout=60000&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:10.746 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@5fb0d4d1; definition: {owner=winit, init=false, side=consumer, release=2.7.16-SNAPSHOT, methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken, dubbo=2.0.2, check=false, interface=com.winit.ums.spi.UmsUserService, version=3.0.0, qos.enable=false, revision=1.2.197-SNAPSHOT, protocol=dubbo, metadata-type=remote, application=cobra-agent, organization=winit, sticky=false}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:11.190 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
11:08:11.307 [main] [] INFO  org.reflections.Reflections - Reflections took 8 ms to scan 1 urls, producing 2 keys and 2 values 
11:08:11.718 [main] [] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
11:08:11.722 [main] [] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:08:11.722 [main] [] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
11:08:11.722 [main] [] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
11:08:11.722 [main] [] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:08:11.722 [main] [] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:08:11.722 [main] [] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
11:08:11.722 [main] [] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@51e68bcd
11:08:11.799 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
11:08:11.801 [main] [] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 9090 is already in use
11:08:11.808 [main] [] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
11:08:11.808 [main] [] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:08:11.808 [main] [] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:08:11.809 [main] [] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:08:11.859 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@5c47ef5f was destroying!
11:08:11.859 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
11:08:11.860 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-9090"]
11:08:11.860 [main] [] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
11:08:11.863 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-9090"]
11:08:11.863 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-9090"]
11:08:11.869 [main] [] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
11:08:11.875 [main] [] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 9090 was already in use.

Action:

Identify and stop the process that's listening on port 9090 or configure this application to listen on another port.

11:08:32.923 [main] [] INFO  o.a.d.common.logger.LoggerFactory - using logger: org.apache.dubbo.common.logger.log4j.Log4jLoggerAdapter
11:08:32.927 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

11:08:32.929 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.apidoc.contextPath=/cobra-agent/dubbo, dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.protocols.whttp.dispatcher=all, dubbo.protocols.whttp.name=whttp, dubbo.protocols.whttp.port=9090, dubbo.protocols.whttp.server=wservlet, dubbo.protocols.whttp.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.provider.version=3.0.0, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.cobra.agent.spi.service,com.winit.gateway.dubbo.swagger.service}
11:08:32.953 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
11:08:32.973 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 70557 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
11:08:32.973 [main] [] INFO  c.w.c.agent.CobraAgentApplication - The following profiles are active: dev
11:08:33.434 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
11:08:33.434 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
11:08:33.434 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
11:08:33.435 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
11:08:33.435 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
11:08:33.635 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
11:08:33.636 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
11:08:33.652 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
11:08:33.704 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:33.704 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
11:08:33.704 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:33.704 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:33.704 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:33.705 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:33.705 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : whttp, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
11:08:33.710 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
11:08:33.744 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
11:08:33.748 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:33.748 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:33.754 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:com.winit.cobra.agent.spi.service.ChatService:3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:33.755 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] The BeanDefinition[Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] of ServiceBean has been registered with name : ServiceBean:com.winit.cobra.agent.spi.service.ConversationService:3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:33.755 [main] [] INFO  o.a.d.c.s.b.f.a.ServiceClassPostProcessor -  [DUBBO] 2 annotated Dubbo's @DubboService Components { [Bean definition with name 'chatService': Generic bean: class [com.winit.cobra.agent.service.ChatServiceImpl]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes/com/winit/cobra/agent/service/ChatServiceImpl.class], Bean definition with name 'conversationService': Generic bean: class [com.winit.cobra.agent.service.ConversationServiceImpl]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes/com/winit/cobra/agent/service/ConversationServiceImpl.class]] } were scanned under package[com.winit.cobra.agent], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:33.762 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
11:08:34.214 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
11:08:34.219 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
11:08:34.220 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
11:08:34.220 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
11:08:34.269 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
11:08:34.269 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1278 ms
11:08:34.731 [main] [] INFO  org.redisson.Version - Redisson 3.20.1
11:08:34.761 [main] [] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at io.netty.resolver.dns.DnsServerAddressStreamProviders.<clinit>(DnsServerAddressStreamProviders.java:64)
	at org.redisson.connection.ServiceManager.<init>(ServiceManager.java:162)
	at org.redisson.config.ConfigSupport.createConnectionManager(ConfigSupport.java:184)
	at org.redisson.Redisson.<init>(Redisson.java:69)
	at org.redisson.Redisson.create(Redisson.java:114)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration.redisson(WinitRedissonAutoConfiguration.java:191)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration$$EnhancerBySpringCGLIB$$676725dd.CGLIB$redisson$0(<generated>)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration$$EnhancerBySpringCGLIB$$676725dd$$FastClassBySpringCGLIB$$40cc603c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.winit.distributed.lock.autoconfig.WinitRedissonAutoConfiguration$$EnhancerBySpringCGLIB$$676725dd.redisson(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1380)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:521)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.winit.cobra.agent.CobraAgentApplication.main(CobraAgentApplication.java:14)
Caused by: java.lang.UnsatisfiedLinkError: 'io.netty.resolver.dns.macos.DnsResolver[] io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.resolvers()'
	at io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.resolvers(Native Method)
	at io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.retrieveCurrentMappings(MacOSDnsServerAddressStreamProvider.java:127)
	at io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider.<init>(MacOSDnsServerAddressStreamProvider.java:123)
	... 114 common frames omitted
11:08:34.851 [main] [] INFO  o.r.cluster.ClusterConnectionManager - Redis cluster nodes configuration got from ************/************:6380:
8fc5aef94cb00c43b94b4e70d357932efbab5804 ************:6379@16379 master - 0 1742872105000 8 connected 5462-10922
fade1bcb8d0ac21d3fa1ba8946e00f5e2453be98 ************:6380@16380 slave 01d5d53c96d71e836896b141a1b6789b891a41c9 0 1742872104000 12 connected
01d5d53c96d71e836896b141a1b6789b891a41c9 ************:6379@16379 master - 0 1742872104420 12 connected 0-5461
c6b554c1d8278a109d8ce273c017c81bc533820f ************:6379@16379 slave a7aadadc1d5397d8b191d7a873eb9f8a80bc604c 0 1742872104000 11 connected
f83dc570be6f83a2efaf9d0cdaa0052152f87bd8 ************:6380@16380 slave 8fc5aef94cb00c43b94b4e70d357932efbab5804 0 1742872104000 8 connected
a7aadadc1d5397d8b191d7a873eb9f8a80bc604c ************:6380@16380 myself,master - 0 1742872102000 11 connected 10923-16383

11:08:34.878 [redisson-netty-2-16] [] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ************/************:6380
11:08:34.886 [redisson-netty-2-19] [] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ************/************:6379
11:08:34.904 [redisson-netty-2-2] [] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ************/************:6379
11:08:35.202 [redisson-netty-2-23] [] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ************/************:6380
11:08:35.213 [redisson-netty-2-27] [] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ************/************:6379
11:08:35.220 [redisson-netty-2-29] [] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ************/************:6379
11:08:35.227 [redisson-netty-2-31] [] INFO  o.r.c.pool.PubSubConnectionPool - 1 connections initialized for ************/************:6379
11:08:35.237 [redisson-netty-2-16] [] INFO  o.r.c.pool.PubSubConnectionPool - 1 connections initialized for ************/************:6380
11:08:35.263 [redisson-netty-2-22] [] INFO  o.r.c.pool.PubSubConnectionPool - 1 connections initialized for ************/************:6380
11:08:35.541 [redisson-netty-2-17] [] INFO  o.r.cluster.ClusterConnectionManager - slaves: [redis://************:6379] added for slot ranges: [[10923-16383]]
11:08:35.541 [redisson-netty-2-17] [] INFO  o.r.cluster.ClusterConnectionManager - master: redis://************:6380 added for slot ranges: [[10923-16383]]
11:08:35.541 [redisson-netty-2-17] [] INFO  o.r.c.pool.SlaveConnectionPool - 24 connections initialized for ************/************:6379
11:08:35.553 [redisson-netty-2-21] [] INFO  o.r.cluster.ClusterConnectionManager - slaves: [redis://************:6380] added for slot ranges: [[0-5461]]
11:08:35.553 [redisson-netty-2-21] [] INFO  o.r.cluster.ClusterConnectionManager - master: redis://************:6379 added for slot ranges: [[0-5461]]
11:08:35.553 [redisson-netty-2-21] [] INFO  o.r.c.pool.SlaveConnectionPool - 24 connections initialized for ************/************:6380
11:08:35.568 [redisson-netty-2-22] [] INFO  o.r.cluster.ClusterConnectionManager - slaves: [redis://************:6380] added for slot ranges: [[5462-10922]]
11:08:35.568 [redisson-netty-2-22] [] INFO  o.r.cluster.ClusterConnectionManager - master: redis://************:6379 added for slot ranges: [[5462-10922]]
11:08:35.568 [redisson-netty-2-22] [] INFO  o.r.c.pool.SlaveConnectionPool - 24 connections initialized for ************/************:6380
11:08:35.701 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
11:08:35.710 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
11:08:35.720 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
11:08:35.726 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
11:08:35.728 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='whttp', host='null', port=9090, contextpath='null', threadpool='null', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='wservlet', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=whttp, port=9090, threads=200, dispatcher=all, server=wservlet}]
11:08:35.735 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random, version=3.0.0}]
11:08:35.746 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
11:08:35.746 [main] [] INFO  o.a.d.r.c.r.ubarrier.DubboSwitcher -  [DUBBO] dubbo已切换到GNONE模式., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.751 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
11:08:35.758 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : zookeeper] supports as the config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.759 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] will be used as the config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.816 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
11:08:35.830 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
11:08:35.835 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
11:08:35.835 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
11:08:35.835 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
11:08:35.835 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
11:08:35.835 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
11:08:35.835 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/com/winit/uschedule/uschedule-autoconfig-support/1.9.5-SNAPSHOT/uschedule-autoconfig-support-1.9.5-20210729.082348-11.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/com/winit/uschedule/uschedule-task-tracker/1.9.5-SNAPSHOT/uschedule-task-tracker-1.9.5-20210729.082336-11.jar:/Users/<USER>/.m2/repository/com/winit/uschedule/uschedule-core/1.9.5-SNAPSHOT/uschedule-core-1.9.5-20210729.082334-11.jar:/Users/<USER>/.m2/repository/commons-dbutils/commons-dbutils/1.6/commons-dbutils-1.6.jar:/Users/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.30/log4j-over-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz/2.3.2/quartz-2.3.2.jar:/Users/<USER>/.m2/repository/com/mchange/mchange-commons-java/0.2.15/mchange-commons-java-0.2.15.jar:/Users/<USER>/.m2/repository/com/101tec/zkclient/0.4/zkclient-0.4.jar:/Users/<USER>/.m2/repository/io/github/tronprotocol/leveldbjni-all/1.18.3/leveldbjni-all-1.18.3.jar:/Users/<USER>/.m2/repository/org/fusesource/hawtjni/hawtjni-runtime/1.18/hawtjni-runtime-1.18.jar:/Users/<USER>/.m2/repository/com/halibobor/leveldb-api/1.18.3/leveldb-api-1.18.3.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/com/winit/distributed-lock-boot-starter/1.0.2-SNAPSHOT/distributed-lock-boot-starter-1.0.2-20230816.024233-2.jar:/Users/<USER>/.m2/repository/com/winit/distributed-lock/1.0.2-SNAPSHOT/distributed-lock-1.0.2-20230816.024231-2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.20.1/redisson-3.20.1.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.65.Final/netty-common-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.65.Final/netty-codec-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.65.Final/netty-buffer-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.65.Final/netty-transport-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.65.Final/netty-resolver-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.65.Final/netty-resolver-dns-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.65.Final/netty-codec-dns-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.65.Final/netty-handler-4.1.65.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.6/rxjava-3.1.6.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jboss-marshalling/2.0.11.Final/jboss-marshalling-2.0.11.Final.jar:/Users/<USER>/.m2/repository/org/jboss/marshalling/jboss-marshalling-river/2.0.11.Final/jboss-marshalling-river-2.0.11.Final.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.4.0/kryo-5.4.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-bean/5.1.6/jodd-bean-5.1.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-core/5.1.6/jodd-core-5.1.6.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.17.5/redisson-spring-boot-starter-3.17.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.4.6/spring-boot-starter-data-redis-2.4.6.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-27/3.17.5/redisson-spring-data-27-3.17.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser/3.5.9/mybatis-plus-jsqlparser-3.5.9.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/5.0/jsqlparser-5.0.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-jsqlparser-common/3.5.9/mybatis-plus-jsqlparser-common-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-common/target/classes:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.1/objenesis-3.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.3.1-SNAPSHOT/gateway-apache-dubbo-1.3.1-20231213.032957-1.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.3.1-SNAPSHOT/gateway-common-1.3.1-20231213.032935-1.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-webflux/2.4.6/spring-boot-starter-webflux-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/2.4.6/spring-boot-starter-reactor-netty-2.4.6.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.0.7/reactor-netty-http-1.0.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.65.Final/netty-codec-http-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.65.Final/netty-codec-http2-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.65.Final/netty-resolver-dns-native-macos-4.1.65.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.65.Final/netty-transport-native-unix-common-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.65.Final/netty-transport-native-epoll-4.1.65.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.0.7/reactor-netty-core-1.0.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.65.Final/netty-handler-proxy-4.1.65.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.65.Final/netty-codec-socks-4.1.65.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/5.3.7/spring-webflux-5.3.7.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.3.2
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
11:08:35.836 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@1c00a9f0
11:08:35.844 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
11:08:35.844 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
11:08:35.849 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
11:08:35.858 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x195b1aee7c701be, negotiated timeout = 60000
11:08:35.861 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
11:08:35.861 [main] [] INFO  o.a.d.r.z.ZookeeperTransporter -  [DUBBO] No valid zookeeper client found from cache, therefore create a new client for url. zookeeper://************:2181/ConfigCenterConfig?check=true&config-file=dubbo.properties&group=dubbo&highest-priority=false&timeout=3000, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.863 [Curator-ConnectionStateManager-0] [] INFO  o.a.d.r.z.c.CuratorZookeeperClient -  [DUBBO] Curator zookeeper client instance initiated successfully, session id is 195b1aee7c701be, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.881 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No global configuration in config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.881 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No application level configuration in config center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.888 [main] [] INFO  o.a.d.c.utils.ConfigValidationUtils -  [DUBBO] No valid monitor config found, specify monitor info to enable collection of Dubbo statistics, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.892 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : zookeeper] supports as the metadata center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.892 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] The registry[<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] will be used as the metadata center, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.899 [main] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] Load service store file /Users/<USER>/.dubbo/dubbo-metadata-cobra-agent-************-2181.cache, data: {com.winit.ums.spi.UmsUserService:3.0.0::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken","dubbo":"2.0.2","check":"false","interface":"com.winit.ums.spi.UmsUserService","version":"3.0.0","qos.enable":"false","revision":"1.2.197-SNAPSHOT","protocol":"dubbo","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.cobra.service.ChatService:::provider:cobra-agent={"annotations":[],"canonicalName":"com.winit.cobra.service.ChatService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/","methods":[{"annotations":[],"name":"chat","parameterTypes":["com.winit.cobra.dto.request.ChatCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.ChatVo"},{"annotations":[],"name":"streamChat","parameterTypes":["com.winit.cobra.dto.request.StreamChatCommand"],"parameters":[],"returnType":"reactor.core.publisher.Flux"},{"annotations":[],"name":"listChat","parameterTypes":["com.winit.cobra.dto.request.ListChatCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.ChatMessagesVo"},{"annotations":[],"name":"listLatestChat","parameterTypes":["com.winit.cobra.dto.request.ListLatestChatCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.LastedChatMessagesVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"chat,streamChat,listChat,listLatestChat","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.service.ChatService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.service.ChatService","threads":"200","generic":"false","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enums":[],"items":[],"properties":{"chatMessages":{"enums":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.dto.response.ChatMessageVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"},"conversationId":{"enums":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"com.winit.cobra.dto.response.LastedChatMessagesVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"chatMessages":{"enums":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.dto.response.ChatMessageVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.dto.response.ChatMessagesVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"month":{"enums":[],"items":[],"properties":{},"type":"short","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"year":{"enums":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"day":{"$ref":"$.types[2].properties.month"}},"type":"java.time.LocalDate","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{},"type":"reactor.core.publisher.Flux","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{},"type":"byte","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.conversationId"},"message":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"},"username":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.request.StreamChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"role":{"$ref":"$.types[0].properties.conversationId"},"content":{"$ref":"$.types[0].properties.conversationId"},"timestamp":{"enums":[],"items":[],"properties":{"date":{"$ref":"$.types[2]"},"time":{"enums":[],"items":[],"properties":{"hour":{"$ref":"$.types[4]"},"nano":{"$ref":"$.types[2].properties.year"},"minute":{"$ref":"$.types[4]"},"second":{"$ref":"$.types[4]"}},"type":"java.time.LocalTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"java.time.LocalDateTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"com.winit.cobra.dto.response.ChatMessageVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[6].properties.timestamp"},{"enums":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.conversationId"},"message":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"},"username":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.request.ChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"},"topN":{"$ref":"$.types[2].properties.year"}},"type":"com.winit.cobra.dto.request.ListChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.month"},{"$ref":"$.types[0].properties.conversationId"},{"enums":[],"items":[],"properties":{"conversionId":{"$ref":"$.types[0].properties.conversationId"},"content":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.response.ChatVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.year"},{"$ref":"$.types[6].properties.timestamp.properties.time"},{"enums":[],"items":[],"properties":{"userId":{"$ref":"$.types[0].properties.conversationId"},"topN":{"$ref":"$.types[2].properties.year"}},"type":"com.winit.cobra.dto.request.ListLatestChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.service.ChatService@file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/"}, com.winit.cobra.service.ConversationService:::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"createConversation,listConversations","injvm":"true","dubbo":"2.0.2","check":"false","interface":"com.winit.cobra.service.ConversationService","qos.enable":"false","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.cobra.service.ChatService:::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"chat,streamChat,listChat,listLatestChat","injvm":"true","dubbo":"2.0.2","check":"false","interface":"com.winit.cobra.service.ChatService","qos.enable":"false","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.ums.spi.UmsUserService:::consumer:cobra-agent={"owner":"winit","init":"false","side":"consumer","release":"2.7.16-SNAPSHOT","methods":"resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken","dubbo":"2.0.2","check":"false","interface":"com.winit.ums.spi.UmsUserService","qos.enable":"false","revision":"1.2.197-SNAPSHOT","metadata-type":"remote","application":"cobra-agent","organization":"winit","sticky":"false"}, com.winit.cobra.agent.spi.service.ConversationService:3.0.0::provider:cobra-agent={"annotations":["@io.swagger.annotations.ApiModel(reference=\"\", parent=java.lang.Void.class, description=\"\\u4f1a\\u8bdd\\u7ba1\\u7406\", subTypes={}, value=\"\", discriminator=\"\")"],"canonicalName":"com.winit.cobra.agent.spi.service.ConversationService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/","methods":[{"annotations":[],"name":"createConversation","parameterTypes":["com.winit.cobra.agent.spi.dto.request.CreateConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.CreateConversationVo"},{"annotations":[],"name":"listConversations","parameterTypes":["com.winit.cobra.agent.spi.dto.request.ListConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.PageConversationsVo"},{"annotations":[],"name":"startConversation","parameterTypes":["com.winit.cobra.agent.spi.dto.request.StartConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.StartConversationVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"startConversation,createConversation,listConversations","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.agent.spi.service.ConversationService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0","threads":"200","version":"3.0.0","generic":"false","revision":"3.0.0","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enum":[],"items":[],"properties":{"created":{"enum":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"conversationId":{"$ref":"$.types[0].properties.created"},"customerCode":{"$ref":"$.types[0].properties.created"},"active":{"enum":[],"items":[],"properties":{},"type":"boolean","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"userId":{"$ref":"$.types[0].properties.created"},"updated":{"$ref":"$.types[0].properties.created"},"customerName":{"$ref":"$.types[0].properties.created"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.response.ListConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"requestUrl":{"$ref":"$.types[0].properties.created"},"attributes":{"enum":[],"items":[{"$ref":"$.types[0].properties.created"},{"enum":[],"items":[],"properties":{},"type":"java.lang.Object","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"ipList":{"enum":[],"items":[],"properties":{},"type":"java.util.Collection<java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.common.spi.context.CommandContext","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"ctx":{"$ref":"$.types[2]"},"userId":{"$ref":"$.types[0].properties.created"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.request.CreateConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"long","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"startNewConversation":{"$ref":"$.types[0].properties.active"},"ctx":{"$ref":"$.types[2]"},"customerCode":{"$ref":"$.types[0].properties.created"},"userId":{"$ref":"$.types[0].properties.created"},"customerName":{"$ref":"$.types[0].properties.created"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.request.StartConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.response.StartConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.attributes.items[1]"},{"$ref":"$.types[0].properties.active"},{"enum":[],"items":[],"properties":{"total":{"$ref":"$.types[4]"},"conversationVos":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.response.ListConversationVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.agent.spi.dto.response.PageConversationsVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[0].properties.created"},{"enum":[],"items":[],"properties":{"conversationId":{"$ref":"$.types[0].properties.created"},"pageNo":{"$ref":"$.types[1]"},"ctx":{"$ref":"$.types[2]"},"customerCode":{"$ref":"$.types[0].properties.created"},"startTime":{"$ref":"$.types[0].properties.created"},"endTime":{"$ref":"$.types[0].properties.created"},"userId":{"$ref":"$.types[0].properties.created"},"customerName":{"$ref":"$.types[0].properties.created"},"topN":{"$ref":"$.types[1]"},"username":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.request.ListConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"chatId":{"$ref":"$.types[0].properties.created"},"conversationId":{"$ref":"$.types[0].properties.created"}},"type":"com.winit.cobra.agent.spi.dto.response.CreateConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.agent.spi.service.ConversationService@file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/"}, com.winit.cobra.agent.spi.service.ChatService:3.0.0::provider:cobra-agent={"annotations":["@io.swagger.annotations.ApiModel(reference=\"\", parent=java.lang.Void.class, description=\"\\u804a\\u5929\\u670d\\u52a1\", subTypes={}, value=\"\", discriminator=\"\")"],"canonicalName":"com.winit.cobra.agent.spi.service.ChatService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/","methods":[{"annotations":[],"name":"chat","parameterTypes":["com.winit.cobra.agent.spi.dto.request.ChatCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.ChatVo"},{"annotations":[],"name":"streamChat","parameterTypes":["com.winit.cobra.agent.spi.dto.request.StreamChatCommand"],"parameters":[],"returnType":"reactor.core.publisher.Flux"},{"annotations":[],"name":"listChat","parameterTypes":["com.winit.cobra.agent.spi.dto.request.ListChatCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo"},{"annotations":[],"name":"transferToHuman","parameterTypes":["com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand"],"parameters":[],"returnType":"com.winit.cobra.agent.spi.dto.response.TransferToHumanVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"transferToHuman,chat,streamChat,listChat","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.agent.spi.service.ChatService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0","threads":"200","version":"3.0.0","generic":"false","revision":"3.0.0","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enum":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"requestUrl":{"enum":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"attributes":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"enum":[],"items":[],"properties":{},"type":"java.lang.Object","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"ipList":{"enum":[],"items":[],"properties":{},"type":"java.util.Collection<java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.common.spi.context.CommandContext","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"toolContext":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.attributes.items[1]"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"ctx":{"$ref":"$.types[1]"},"customerCode":{"$ref":"$.types[1].properties.requestUrl"},"message":{"$ref":"$.types[1].properties.requestUrl"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"customerName":{"$ref":"$.types[1].properties.requestUrl"},"customVariables":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"metaData":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"autoSaveHistory":{"enum":[],"items":[],"properties":{},"type":"boolean","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"toolCallOut":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.request.SubmitToolCallCommand>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.StreamChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"metaData":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"autoSaveHistory":{"$ref":"$.types[2].properties.autoSaveHistory"},"toolContext":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.attributes.items[1]"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.Object>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"ctx":{"$ref":"$.types[1]"},"toolCallOut":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.request.SubmitToolCallCommand>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"},"message":{"$ref":"$.types[1].properties.requestUrl"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"customVariables":{"enum":[],"items":[{"$ref":"$.types[1].properties.requestUrl"},{"$ref":"$.types[1].properties.requestUrl"}],"properties":{},"type":"java.util.Map<java.lang.String,java.lang.String>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.MapTypeBuilder"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.ChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"long","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.attributes.items[1]"},{"enum":[],"items":[],"properties":{},"type":"java.lang.Boolean","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"createdTimeDesc":{"$ref":"$.types[2].properties.autoSaveHistory"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"pageNo":{"$ref":"$.types[0]"},"ctx":{"$ref":"$.types[1]"},"startTime":{"$ref":"$.types[1].properties.requestUrl"},"endTime":{"$ref":"$.types[1].properties.requestUrl"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"topN":{"$ref":"$.types[0]"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.ListChatCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[2].properties.autoSaveHistory"},{"enum":[],"items":[],"properties":{"humanAgentType":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"ctx":{"$ref":"$.types[1]"},"userId":{"$ref":"$.types[1].properties.requestUrl"},"transferIdentity":{"$ref":"$.types[1].properties.requestUrl"},"username":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.requestUrl"},{"enum":[],"items":[],"properties":{"total":{"$ref":"$.types[4]"},"chatMessages":{"enum":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.agent.spi.dto.response.ChatMessageVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"reactor.core.publisher.Flux","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"logId":{"$ref":"$.types[1].properties.requestUrl"},"content":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.response.ChatVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"output":{"$ref":"$.types[1].properties.requestUrl"},"toolCallId":{"$ref":"$.types[1].properties.requestUrl"},"functionName":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.request.SubmitToolCallCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{},"type":"com.winit.cobra.agent.spi.dto.response.TransferToHumanVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enum":[],"items":[],"properties":{"role":{"$ref":"$.types[1].properties.requestUrl"},"chatId":{"$ref":"$.types[1].properties.requestUrl"},"conversationId":{"$ref":"$.types[1].properties.requestUrl"},"likeOrDislike":{"$ref":"$.types[6]"},"debugContent":{"$ref":"$.types[1].properties.requestUrl"},"content":{"$ref":"$.types[1].properties.requestUrl"},"feedbackInfo":{"$ref":"$.types[1].properties.requestUrl"},"timestamp":{"$ref":"$.types[1].properties.requestUrl"}},"type":"com.winit.cobra.agent.spi.dto.response.ChatMessageVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.agent.spi.service.ChatService@file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/"}, com.winit.cobra.service.ConversationService:::provider:cobra-agent={"annotations":[],"canonicalName":"com.winit.cobra.service.ConversationService","codeSource":"file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/","methods":[{"annotations":[],"name":"createConversation","parameterTypes":["com.winit.cobra.dto.request.CreateConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.CreateConversationVo"},{"annotations":[],"name":"listConversations","parameterTypes":["com.winit.cobra.dto.request.ListConversationCommand"],"parameters":[],"returnType":"com.winit.cobra.dto.response.ListConversationsVo"}],"parameters":{"cluster":"failover","release":"2.7.16-SNAPSHOT","methods":"createConversation,listConversations","deprecated":"false","dubbo":"2.0.2","loadbalance":"random","interface":"com.winit.cobra.service.ConversationService","threadpool":"limited","qos.enable":"false","timeout":"60000","dynamic":"true","executes":"500","dispatcher":"all","validation":"true","anyhost":"true","owner":"winit","side":"provider","service.name":"ServiceBean:/com.winit.cobra.service.ConversationService","threads":"200","generic":"false","retries":"2","metadata-type":"remote","application":"cobra-agent","organization":"winit"},"types":[{"enums":[],"items":[],"properties":{"conversationId":{"enums":[],"items":[],"properties":{},"type":"java.lang.String","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"com.winit.cobra.dto.response.CreateConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"date":{"enums":[],"items":[],"properties":{"month":{"enums":[],"items":[],"properties":{},"type":"short","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"year":{"enums":[],"items":[],"properties":{},"type":"int","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"day":{"$ref":"$.types[1].properties.date.properties.month"}},"type":"java.time.LocalDate","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"time":{"enums":[],"items":[],"properties":{"hour":{"enums":[],"items":[],"properties":{},"type":"byte","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},"nano":{"$ref":"$.types[1].properties.date.properties.year"},"minute":{"$ref":"$.types[1].properties.time.properties.hour"},"second":{"$ref":"$.types[1].properties.time.properties.hour"}},"type":"java.time.LocalTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}},"type":"java.time.LocalDateTime","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"userId":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.request.CreateConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"enums":[],"items":[],"properties":{"userId":{"$ref":"$.types[0].properties.conversationId"},"topN":{"$ref":"$.types[1].properties.date.properties.year"}},"type":"com.winit.cobra.dto.request.ListConversationCommand","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.date.properties.month"},{"$ref":"$.types[0].properties.conversationId"},{"$ref":"$.types[1].properties.date"},{"enums":[],"items":[],"properties":{"conversationVos":{"enums":[],"items":[],"properties":{},"type":"java.util.List<com.winit.cobra.dto.response.ListConversationVo>","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.CollectionTypeBuilder"}},"type":"com.winit.cobra.dto.response.ListConversationsVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"},{"$ref":"$.types[1].properties.date.properties.year"},{"$ref":"$.types[1].properties.time"},{"$ref":"$.types[1].properties.time.properties.hour"},{"enums":[],"items":[],"properties":{"lastUpdated":{"$ref":"$.types[1]"},"conversationId":{"$ref":"$.types[0].properties.conversationId"},"userId":{"$ref":"$.types[0].properties.conversationId"}},"type":"com.winit.cobra.dto.response.ListConversationVo","typeBuilderName":"org.apache.dubbo.metadata.definition.builder.DefaultTypeBuilder"}],"uniqueId":"com.winit.cobra.service.ConversationService@file:/Users/<USER>/CodeSpace/cobra-agent/target/classes/"}}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.911 [main] [] INFO  o.a.d.r.z.ZookeeperTransporter -  [DUBBO] find valid zookeeper client from the cache for address: zookeeper://************:2181/org.apache.dubbo.metadata.report.MetadataReport?application=cobra-agent&client=&port=2181&protocol=zookeeper, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.913 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has been initialized!, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.959 [main] [] INFO  o.a.d.q.protocol.QosProtocolWrapper -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.962 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Load registry cache file /Users/<USER>/.dubbo/dubbo-registry-cobra-agent-************-2181.cache, data: {com.winit.cobra.agent.spi.service.ConversationService:3.0.0=empty://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=8922&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742815367872&validation=true&version=3.0.0, com.winit.cobra.agent.spi.service.ChatService:3.0.0=empty://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=8922&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742815367937&validation=true&version=3.0.0, com.winit.cobra.service.ConversationService=empty://************:20812/com.winit.cobra.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=************&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.service.ConversationService&loadbalance=random&metadata-type=remote&methods=createConversation,listConversations&organization=winit&owner=winit&pid=94870&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&service.name=ServiceBean:/com.winit.cobra.service.ConversationService&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1736505562109&validation=true, com.winit.cobra.service.ChatService=empty://************:20812/com.winit.cobra.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=************&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.service.ChatService&loadbalance=random&metadata-type=remote&methods=chat,streamChat,listChat,listLatestChat&organization=winit&owner=winit&pid=94870&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&service.name=ServiceBean:/com.winit.cobra.service.ChatService&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&validation=true, com.winit.ums.spi.UmsUserService:3.0.0=empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0 empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=configurators&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70319&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0 dubbo://************:2488/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0 whttp://************:2480/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&server=servlet&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0, com.winit.ums.spi.UmsUserService=empty://************/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=83619&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=************* empty://************/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=configurators&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=83619&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=************* empty://************/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,queryAllAccountByCode,validateLoginPassword,batchResetPassword,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=83619&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.962 [main] [] INFO  o.a.d.r.z.ZookeeperTransporter -  [DUBBO] find valid zookeeper client from the cache for address: zookeeper://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&interface=org.apache.dubbo.registry.RegistryService&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&timestamp=*************, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.970 [main] [] INFO  o.a.d.r.c.m.MigrationRuleListener -  [DUBBO] Listening for migration rules on dataId-cobra-agent.migration group-MIGRATION, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.993 [main] [] INFO  o.a.d.r.c.m.MigrationRuleListener -  [DUBBO] Using the following migration rule to migrate:, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:35.993 [main] [] INFO  o.a.d.r.c.m.MigrationRuleListener -  [DUBBO] INIT, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.023 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Register: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=consumers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.058 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Subscribe: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.108 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Notify urls for subscribe url consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, urls: [dubbo://************:2488/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0, whttp://************:2480/com.winit.ums.spi.UmsUserService?anyhost=true&application=ums&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,resetToken,validateLoginPassword,batchResetPassword,queryAllAccountByCode,checkEmail,queryByUsername,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,queryAccountByCompanyIdAndType,getToken,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken,modifySingleItemUserInfo&organization=winit&owner=xiangqi.zhou&pid=16131&release=2.7.16-SNAPSHOT&retries=2&revision=1.2.198-SNAPSHOT&server=servlet&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=*************&version=3.0.0, empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=configurators&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, empty://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.153 [NettyClientWorker-5-1] [] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /127.0.0.1:57347 -> /127.0.0.1:7897 is established., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.154 [main] [] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Successed connect to server /127.0.0.1:7897 from NettyClient *********** using dubbo version 2.7.16-SNAPSHOT, channel is NettyChannel [channel=[id: 0x34c0a916, L:/127.0.0.1:57347 - R:/127.0.0.1:7897]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.154 [main] [] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Start NettyClient /*********** connect to the server /127.0.0.1:7897, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.173 [main] [] INFO  o.a.dubbo.config.ReferenceConfig -  [DUBBO] Refer dubbo service com.winit.ums.spi.UmsUserService from url dubbo://***********/com.winit.ums.spi.UmsUserService?anyhost=true&application=cobra-agent&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&init=false&interface=com.winit.ums.spi.UmsUserService&loadbalance=random&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&register.ip=***********&release=2.7.16-SNAPSHOT&remote.application=ums&retries=2&revision=1.2.197-SNAPSHOT&service.filter=dubboProviderFilter,dubboProviderPrometheusFilter&service.name=ServiceBean:/com.winit.ums.spi.UmsUserService:3.0.0&side=consumer&sticky=false&timeout=60000&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.264 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@7de9bcfb; definition: {owner=winit, init=false, side=consumer, release=2.7.16-SNAPSHOT, methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken, dubbo=2.0.2, check=false, interface=com.winit.ums.spi.UmsUserService, version=3.0.0, qos.enable=false, revision=1.2.197-SNAPSHOT, protocol=dubbo, metadata-type=remote, application=cobra-agent, organization=winit, sticky=false}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:36.721 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
11:08:36.836 [main] [] INFO  org.reflections.Reflections - Reflections took 7 ms to scan 1 urls, producing 2 keys and 2 values 
11:08:37.255 [main] [] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
11:08:37.260 [main] [] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:08:37.260 [main] [] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
11:08:37.260 [main] [] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
11:08:37.261 [main] [] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:08:37.261 [main] [] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:08:37.261 [main] [] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
11:08:37.261 [main] [] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1989e9ec
11:08:37.338 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
11:08:37.344 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
11:08:37.533 [main] [] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
11:08:37.533 [main] [] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:08:37.548 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is starting..., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.562 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to get local host., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.591 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service com.winit.cobra.agent.spi.service.ConversationService to local registry url : injvm://127.0.0.1/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.591 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service com.winit.cobra.agent.spi.service.ConversationService url whttp://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&validation=true&version=3.0.0 to registry registry://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&organization=winit&owner=winit&pid=70557&qos.enable=false&registry=zookeeper&release=2.7.16-SNAPSHOT&timestamp=1742872117554, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.603 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Register: whttp://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.613 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Subscribe: provider://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.625 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Notify urls for subscribe url provider://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&validation=true&version=3.0.0, urls: [empty://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&validation=true&version=3.0.0], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.627 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@7aaad1c7; definition: FullServiceDefinition{parameters={cluster=failover, server=wservlet, release=2.7.16-SNAPSHOT, methods=startConversation,createConversation,listConversations, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ConversationService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ConversationService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=createConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.CreateConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.CreateConversationVo], MethodDefinition [name=listConversations, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageConversationsVo], MethodDefinition [name=startConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StartConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.StartConversationVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.627 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to get local host., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.627 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service com.winit.cobra.agent.spi.service.ConversationService to local registry url : injvm://127.0.0.1/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.627 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service com.winit.cobra.agent.spi.service.ConversationService url dubbo://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&validation=true&version=3.0.0 to registry registry://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&organization=winit&owner=winit&pid=70557&qos.enable=false&registry=zookeeper&release=2.7.16-SNAPSHOT&timestamp=1742872117554, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.635 [main] [] INFO  o.a.d.r.transport.AbstractServer -  [DUBBO] Start NettyServer bind /0.0.0.0:20812, export /***********:20812, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.636 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Register: dubbo://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.642 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Subscribe: provider://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.650 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Notify urls for subscribe url provider://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&validation=true&version=3.0.0, urls: [empty://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&validation=true&version=3.0.0], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.655 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to get local host., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.657 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service com.winit.cobra.agent.spi.service.ChatService to local registry url : injvm://127.0.0.1/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.658 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service com.winit.cobra.agent.spi.service.ChatService url whttp://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&validation=true&version=3.0.0 to registry registry://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&organization=winit&owner=winit&pid=70557&qos.enable=false&registry=zookeeper&release=2.7.16-SNAPSHOT&timestamp=1742872117652, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.659 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@12fd5ddf; definition: FullServiceDefinition{parameters={cluster=failover, release=2.7.16-SNAPSHOT, methods=startConversation,createConversation,listConversations, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ConversationService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ConversationService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=createConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.CreateConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.CreateConversationVo], MethodDefinition [name=listConversations, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageConversationsVo], MethodDefinition [name=startConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StartConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.StartConversationVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.666 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Register: whttp://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.681 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Subscribe: provider://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.693 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Notify urls for subscribe url provider://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&validation=true&version=3.0.0, urls: [empty://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&validation=true&version=3.0.0], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.695 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@21047794; definition: FullServiceDefinition{parameters={cluster=failover, server=wservlet, release=2.7.16-SNAPSHOT, methods=transferToHuman,chat,streamChat,listChat, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ChatService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ChatService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=chat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.ChatVo], MethodDefinition [name=streamChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StreamChatCommand], returnType=reactor.core.publisher.Flux], MethodDefinition [name=listChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo], MethodDefinition [name=transferToHuman, parameterTypes=[com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand], returnType=com.winit.cobra.agent.spi.dto.response.TransferToHumanVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.695 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] No valid ip found from environment, try to get local host., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.695 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Export dubbo service com.winit.cobra.agent.spi.service.ChatService to local registry url : injvm://127.0.0.1/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.695 [main] [] INFO  o.apache.dubbo.config.ServiceConfig -  [DUBBO] Register dubbo service com.winit.cobra.agent.spi.service.ChatService url dubbo://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&validation=true&version=3.0.0 to registry registry://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&organization=winit&owner=winit&pid=70557&qos.enable=false&registry=zookeeper&release=2.7.16-SNAPSHOT&timestamp=1742872117652, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.702 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Register: dubbo://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.710 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Subscribe: provider://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.720 [main] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Notify urls for subscribe url provider://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&validation=true&version=3.0.0, urls: [empty://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&validation=true&version=3.0.0], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.721 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@f22ca57; definition: FullServiceDefinition{parameters={cluster=failover, release=2.7.16-SNAPSHOT, methods=transferToHuman,chat,streamChat,listChat, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ChatService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ChatService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=chat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.ChatVo], MethodDefinition [name=streamChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StreamChatCommand], returnType=reactor.core.publisher.Flux], MethodDefinition [name=listChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo], MethodDefinition [name=transferToHuman, parameterTypes=[com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand], returnType=com.winit.cobra.agent.spi.dto.response.TransferToHumanVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.722 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap is ready., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.723 [main] [] INFO  o.a.d.c.bootstrap.DubboBootstrap -  [DUBBO] DubboBootstrap has started., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:08:37.726 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 4.943 seconds (JVM running for 5.51)
11:08:38.041 [RMI TCP Connection(7)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
11:08:38.042 [RMI TCP Connection(9)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:08:38.042 [RMI TCP Connection(9)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
11:08:38.043 [RMI TCP Connection(9)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
11:08:38.171 [RMI TCP Connection(7)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
11:09:15.588 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.queryChatDebugInfo","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"6A9D575883548170A3656B6560A5207B","data":{"chatId":"","conversationId":"7485328228417110042","userId":3109,"username":"<EMAIL>","message":"如何进行退货操作"},"format":"json","language":"zh_CN","platform":"coze","sign":"6A9D575883548170A3656B6560A5207B","sign_method":"md5","timestamp":"1742459852086","version":"1.0"}
11:09:15.704 [http-nio-9090-exec-1] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
java.lang.NullPointerException: Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
	at com.winit.cobra.agent.manager.impl.ChatManagerImpl.queryChatDebugInfo(ChatManagerImpl.java:191)
	at com.winit.cobra.agent.service.ChatDebugInfoServiceImpl.queryChatDebugInfo(ChatDebugInfoServiceImpl.java:35)
	at com.winit.cobra.agent.controller.ChatController.queryChatDebugInfo(ChatController.java:156)
	at com.winit.cobra.agent.controller.ChatController$$FastClassBySpringCGLIB$$715bfcc0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:77)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$6d7c8a0c.queryChatDebugInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
11:09:15.707 [http-nio-9090-exec-1] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
java.lang.NullPointerException: Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
	at com.winit.cobra.agent.manager.impl.ChatManagerImpl.queryChatDebugInfo(ChatManagerImpl.java:191)
	at com.winit.cobra.agent.service.ChatDebugInfoServiceImpl.queryChatDebugInfo(ChatDebugInfoServiceImpl.java:35)
	at com.winit.cobra.agent.controller.ChatController.queryChatDebugInfo(ChatController.java:156)
	at com.winit.cobra.agent.controller.ChatController$$FastClassBySpringCGLIB$$715bfcc0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:77)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$6d7c8a0c.queryChatDebugInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
11:09:30.776 [http-nio-9090-exec-2] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.queryChatDebugInfo","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"6A9D575883548170A3656B6560A5207B","data":{"chatId":"","conversationId":"7485328228417110042","userId":3109,"username":"<EMAIL>","message":"如何进行退货操作"},"format":"json","language":"zh_CN","platform":"coze","sign":"6A9D575883548170A3656B6560A5207B","sign_method":"md5","timestamp":"1742459852086","version":"1.0"}
11:09:39.741 [http-nio-9090-exec-2] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
java.lang.NullPointerException: Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
	at com.winit.cobra.agent.manager.impl.ChatManagerImpl.queryChatDebugInfo(ChatManagerImpl.java:191)
	at com.winit.cobra.agent.service.ChatDebugInfoServiceImpl.queryChatDebugInfo(ChatDebugInfoServiceImpl.java:35)
	at com.winit.cobra.agent.controller.ChatController.queryChatDebugInfo(ChatController.java:156)
	at com.winit.cobra.agent.controller.ChatController$$FastClassBySpringCGLIB$$715bfcc0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:77)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$6d7c8a0c.queryChatDebugInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
11:09:39.743 [http-nio-9090-exec-2] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
java.lang.NullPointerException: Cannot invoke "com.winit.cobra.agent.repository.mybatis.entity.ChatMessageHistoryEntity.getDebugContent()" because "chatMessageHistoryEntity" is null
	at com.winit.cobra.agent.manager.impl.ChatManagerImpl.queryChatDebugInfo(ChatManagerImpl.java:191)
	at com.winit.cobra.agent.service.ChatDebugInfoServiceImpl.queryChatDebugInfo(ChatDebugInfoServiceImpl.java:35)
	at com.winit.cobra.agent.controller.ChatController.queryChatDebugInfo(ChatController.java:156)
	at com.winit.cobra.agent.controller.ChatController$$FastClassBySpringCGLIB$$715bfcc0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:77)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$6d7c8a0c.queryChatDebugInfo(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
11:09:54.881 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.queryChatDebugInfo","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"6A9D575883548170A3656B6560A5207B","data":{"chatId":"7485574974795956239","conversationId":"7485328228417110042","userId":3109,"username":"<EMAIL>","message":"如何进行退货操作"},"format":"json","language":"zh_CN","platform":"coze","sign":"6A9D575883548170A3656B6560A5207B","sign_method":"md5","timestamp":"1742459852086","version":"1.0"}
11:12:49.905 [Thread-1] [] INFO  c.w.g.d.protocol.http.HttpProtocol - ====================> jvm shutdown <==================
11:12:49.914 [SpringContextShutdownHook] [] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.919 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: whttp://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.925 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117554&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.926 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: dubbo://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.926 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.935 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117627&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.935 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.935 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: whttp://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.943 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117652&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.943 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.943 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: dubbo://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.952 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742872117694&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.952 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.955 [SpringContextShutdownHook] [] INFO  o.a.d.c.e.l.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.955 [SpringContextShutdownHook] [] INFO  o.a.d.r.s.AbstractRegistryFactory -  [DUBBO] Close all registries [zookeeper://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&interface=org.apache.dubbo.registry.RegistryService&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&timestamp=*************], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.955 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Destroy registry:zookeeper://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&interface=org.apache.dubbo.registry.RegistryService&organization=winit&owner=winit&pid=70557&qos.enable=false&release=2.7.16-SNAPSHOT&timestamp=*************, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.955 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=consumers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.967 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Destroy unregister url consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=consumers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.967 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.967 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Destroy unsubscribe url consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,querySigleChildAccount,getUser,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=70557&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.973 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
11:12:49.986 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x195b1aee7c701be closed
11:12:49.987 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
11:12:49.987 [SpringContextShutdownHook] [] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /***********:20812, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
11:12:49.987 [SpringContextShutdownHook] [] INFO  o.a.d.r.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20812, export /***********:20812, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
