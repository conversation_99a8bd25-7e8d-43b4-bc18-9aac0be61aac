09:50:31.019 [NettyClientWorker-5-1] [] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /127.0.0.1:52354 -> /127.0.0.1:7897 is disconnected., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
09:50:39.757 [dubbo-client-idleCheck-thread-1] [] INFO  o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Initial connection to HeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/127.0.0.1:52354 -> /127.0.0.1:7897]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
09:50:39.758 [dubbo-client-idleCheck-thread-1] [] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x8db38fef, L:/127.0.0.1:52354 ! R:/127.0.0.1:7897], dubbo version: 2.7.16-<PERSON><PERSON><PERSON><PERSON><PERSON>, current host: ***********
09:50:39.764 [dubbo-client-idleCheck-thread-1] [] INFO  o.a.d.r.transport.netty4.NettyClient -  [DUBBO] Close old netty channel [id: 0x8db38fef, L:/127.0.0.1:52354 ! R:/127.0.0.1:7897] on create new netty channel [id: 0x1c02f69d, L:/127.0.0.1:56241 - R:/127.0.0.1:7897], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
09:50:39.764 [dubbo-client-idleCheck-thread-1] [] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Successed connect to server /127.0.0.1:7897 from NettyClient *********** using dubbo version 2.7.16-SNAPSHOT, channel is NettyChannel [channel=[id: 0x1c02f69d, L:/127.0.0.1:56241 - R:/127.0.0.1:7897]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
09:50:39.764 [NettyClientWorker-5-2] [] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /127.0.0.1:56241 -> /127.0.0.1:7897 is established., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
