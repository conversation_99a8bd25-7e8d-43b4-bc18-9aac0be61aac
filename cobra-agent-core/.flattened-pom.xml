<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.winit</groupId>
  <artifactId>cobra-agent-core</artifactId>
  <version>3.0.0-SNAPSHOT</version>
  <name>cobra-agent-core</name>
  <description>Winit AI Agent</description>
  <url>https://spring.io/projects/spring-boot/cobra-agent/cobra-agent-core</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Pivotal</name>
      <email><EMAIL></email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/spring-projects/spring-boot/cobra-agent/cobra-agent-core</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>releases</id>
      <name>Nexus Repository</name>
      <url>http://***********:8081/nexus/content/repositories/releases</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>Nexus Repository</name>
      <url>http://***********:8081/nexus/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.winit.uschedule</groupId>
      <artifactId>uschedule-autoconfig-support</artifactId>
      <version>1.9.5-SNAPSHOT</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>leveldbjni-all</artifactId>
          <groupId>org.fusesource.leveldbjni</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.github.tronprotocol</groupId>
      <artifactId>leveldbjni-all</artifactId>
      <version>1.18.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>cache-spring-boot-starter</artifactId>
      <version>1.0.3-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>distributed-lock-boot-starter</artifactId>
      <version>1.0.2-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>2.4.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-core</artifactId>
      <version>1.0.0-M4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-observation</artifactId>
      <version>1.14.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>3.5.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-jsqlparser</artifactId>
      <version>3.5.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.25</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
      <version>2.4.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>cobra-agent-coze</artifactId>
      <version>3.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.11</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-core</artifactId>
      <version>5.8.26</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-crypto</artifactId>
      <version>5.8.26</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor.addons</groupId>
      <artifactId>reactor-adapter</artifactId>
      <version>3.4.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <version>2.4.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>cobra-agent-spi</artifactId>
      <version>3.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>spi-ums</artifactId>
      <version>1.2.197-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo</artifactId>
      <version>2.7.16-SNAPSHOT</version>
      <classifier>winit01</classifier>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-recipes</artifactId>
      <version>4.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-framework</artifactId>
      <version>4.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-client</artifactId>
      <version>4.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <version>3.4.6</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
      <version>2.7.15-SNAPSHOT</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>dubbo</artifactId>
          <groupId>org.apache.dubbo</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.winit</groupId>
      <artifactId>gateway-apache-dubbo</artifactId>
      <version>1.3.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>swagger-bootstrap-ui</artifactId>
      <version>1.8.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-boot-starter</artifactId>
      <version>3.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <version>2.4.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
      <version>1.6.7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations</artifactId>
      <version>24.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
      <version>2.4.6</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>winit</id>
      <url>http://***********:8081/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <id>aliyun</id>
      <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
  </repositories>
</project>
