package com.winit.cobra.agent.spi.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 工作流执行响应结果
 * <AUTHOR>
 */
@ApiModel(description = "工作流执行响应结果")
public class RunWorkflowVo extends BaseVo{


    /*
    异步执行的唯一标识ID。仅当工作流被异步执行时返回（is_async=true）。
    可通过该execute_id调用"查询工作流异步执行结果"接口获取工作流最终执行结果。
    * */
    @ApiModelProperty("异步执行的唯一标识ID。仅当工作流被异步执行时返回（is_async=true）。可通过该execute_id调用\"查询工作流异步执行结果\"接口获取工作流最终执行结果。")
    @JsonProperty("execute_id")
    private String executeID;

    /*
    工作流执行结果，通常为JSON序列化字符串。某些场景下可能返回非JSON结构的字符串。
    * */
    @ApiModelProperty("工作流执行结果，通常为JSON序列化字符串。某些场景下可能返回非JSON结构的字符串。")
    @JsonProperty("data")
    private String data;

    @ApiModelProperty(hidden = true)
    @JsonProperty("debug_url")
    private String debugURL;

    @ApiModelProperty(hidden = true)
    @JsonProperty("token")
    private Integer token;

    @ApiModelProperty(hidden = true)
    @JsonProperty("cost")
    private String cost;

    public String getExecuteID() {
        return executeID;
    }

    public void setExecuteID(String executeID) {
        this.executeID = executeID;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getDebugURL() {
        return debugURL;
    }

    public void setDebugURL(String debugURL) {
        this.debugURL = debugURL;
    }

    public Integer getToken() {
        return token;
    }

    public void setToken(Integer token) {
        this.token = token;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }
}