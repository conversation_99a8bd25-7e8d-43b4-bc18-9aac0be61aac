19:01:18.430 [http-nio-9090-exec-6] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"6A9D575883548170A3656B6560A5207B","data":{"chatId":"","conversationId":"7483808696783536128","userId":253041,"username":"<EMAIL>","message":"打开 商品管理页面\\n"},"format":"json","language":"zh_CN","platform":"coze","sign":"6A9D575883548170A3656B6560A5207B","sign_method":"md5","timestamp":"1742459852086","version":"1.0"}
19:01:18.439 [http-nio-9090-exec-6] [] INFO  c.w.d.lock.RedisDistributedLock - 尝试获取分布式锁 Key=Lock-Cobra-agent:chat:253041:7483808696783536128 WaitTime=30000 LeaseTime=180000 TimeUnit=MILLISECONDS
19:01:18.456 [http-nio-9090-exec-6] [] INFO  c.w.d.lock.RedisDistributedLock - 释放分布式锁 Key=Lock-Cobra-agent:chat:253041:7483808696783536128
19:01:28.406 [boundedElastic-1] [] INFO  c.w.c.a.l.RedisDistributedLockTemplate - 会话 chat:253041:7483808696783536128 结束，信号类型: onComplete
19:01:45.398 [http-nio-9090-exec-8] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"6A9D575883548170A3656B6560A5207B","data":{"chatId":"","conversationId":"7483808696783536128","userId":253041,"username":"<EMAIL>","message":"如何进行退货操作"},"format":"json","language":"zh_CN","platform":"coze","sign":"6A9D575883548170A3656B6560A5207B","sign_method":"md5","timestamp":"1742459852086","version":"1.0"}
19:01:45.402 [http-nio-9090-exec-8] [] INFO  c.w.d.lock.RedisDistributedLock - 尝试获取分布式锁 Key=Lock-Cobra-agent:chat:253041:7483808696783536128 WaitTime=30000 LeaseTime=180000 TimeUnit=MILLISECONDS
19:01:45.421 [http-nio-9090-exec-8] [] INFO  c.w.d.lock.RedisDistributedLock - 释放分布式锁 Key=Lock-Cobra-agent:chat:253041:7483808696783536128
19:02:24.232 [boundedElastic-1] [] INFO  c.w.c.a.l.RedisDistributedLockTemplate - 会话 chat:253041:7483808696783536128 结束，信号类型: onComplete
19:02:37.577 [Thread-1] [] INFO  c.w.g.d.protocol.http.HttpProtocol - ====================> jvm shutdown <==================
19:02:37.578 [SpringContextShutdownHook] [] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.581 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: whttp://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=58130&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383176&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.590 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:9090/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=58130&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383176&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.591 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.591 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: dubbo://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=58130&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383253&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.596 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:20812/com.winit.cobra.agent.spi.service.ConversationService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ConversationService&loadbalance=random&metadata-type=remote&methods=startConversation,createConversation,listConversations&organization=winit&owner=winit&pid=58130&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383253&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.596 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.596 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: whttp://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=58130&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383397&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.602 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:9090/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=9090&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=58130&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&server=wservlet&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383397&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.602 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.603 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: dubbo://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=58130&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383435&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.608 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: provider://***********:20812/com.winit.cobra.agent.spi.service.ChatService?anyhost=true&application=cobra-agent&bind.ip=***********&bind.port=20812&category=configurators&check=false&cluster=failover&deprecated=false&dispatcher=all&dubbo=2.0.2&dynamic=true&executes=500&generic=false&interface=com.winit.cobra.agent.spi.service.ChatService&loadbalance=random&metadata-type=remote&methods=transferToHuman,chat,streamChat,listChat&organization=winit&owner=winit&pid=58130&qos.enable=false&release=2.7.16-SNAPSHOT&retries=2&revision=3.0.0&service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0&side=provider&threadpool=limited&threads=200&timeout=60000&timestamp=1742468383435&validation=true&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.609 [Exporter-Unexport-thread-1] [] INFO  o.a.d.r.integration.RegistryProtocol -  [DUBBO] Waiting 10000ms for registry to notify all consumers before unexport. Usually, this is called when you use dubbo API, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.612 [SpringContextShutdownHook] [] INFO  o.a.d.c.e.l.LoggingEventListener -  [DUBBO] Dubbo Service has been destroyed., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.612 [SpringContextShutdownHook] [] INFO  o.a.d.r.s.AbstractRegistryFactory -  [DUBBO] Close all registries [zookeeper://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&interface=org.apache.dubbo.registry.RegistryService&organization=winit&owner=winit&pid=58130&qos.enable=false&release=2.7.16-SNAPSHOT&timestamp=*************], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.612 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Destroy registry:zookeeper://************:2181/org.apache.dubbo.registry.RegistryService?application=cobra-agent&dubbo=2.0.2&dynamic=true&id=org.apache.dubbo.config.RegistryConfig#0&interface=org.apache.dubbo.registry.RegistryService&organization=winit&owner=winit&pid=58130&qos.enable=false&release=2.7.16-SNAPSHOT&timestamp=*************, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.612 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unregister: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=consumers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=58130&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.622 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Destroy unregister url consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=consumers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=58130&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.622 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Unsubscribe: consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=58130&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.622 [SpringContextShutdownHook] [] INFO  o.a.d.r.zookeeper.ZookeeperRegistry -  [DUBBO] Destroy unsubscribe url consumer://***********/com.winit.ums.spi.UmsUserService?application=cobra-agent&category=providers,configurators,routers&check=false&dubbo=2.0.2&init=false&interface=com.winit.ums.spi.UmsUserService&metadata-type=remote&methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken&organization=winit&owner=winit&pid=58130&protocol=dubbo&qos.enable=false&release=2.7.16-SNAPSHOT&revision=1.2.197-SNAPSHOT&side=consumer&sticky=false&timestamp=*************&version=3.0.0, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.623 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:02:37.630 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x195b1aee7c70130 closed
19:02:37.630 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:02:37.630 [SpringContextShutdownHook] [] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Close dubbo server: /***********:20812, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
19:02:37.631 [SpringContextShutdownHook] [] INFO  o.a.d.r.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20812, export /***********:20812, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
