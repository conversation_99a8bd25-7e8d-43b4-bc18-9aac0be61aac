# server port
server.port=9090
spring.application.name=${dubbo.application.name}

# i18n
spring.messages.encoding=UTF-8
spring.messages.basename=i18n/message

#coze
coze.token=XXX
coze.botID=7447371549063626790
coze.userId=user
coze.apiBase=https://api.coze.cn
# 三分半等待时间
coze.readTimeout=180000
coze.connectTimeout=90000
#最大并发请求
coze.maxRequests=180
coze.privateKey=XXX
coze.clientId=1161082478811
coze.publicKeyId=XXX
tool.call.locations=com.winit.cobra.agent.tools
#
#spring.data.mongodb.host=localhost
#spring.data.mongodb.port=27017
#spring.data.mongodb.database=chat
#spring.data.mongodb.username=root
#spring.data.mongodb.password=example
#spring.data.mongodb.authentication-database=admin

############## database
spring.datasource.druid.url=*****************************************************************************************
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.username=root
spring.datasource.druid.password=
################## database pool
# 初始化链接数量（根据业务实际情况调整）
spring.datasource.druid.initial-size=10
# 最大活动连接数（根据业务实际情况调整）
spring.datasource.druid.max-active=30
# 最小空闲连接数（根据业务实际情况调整），
spring.datasource.druid.min-idle=10
# 参数表示从连接池获取连接的超时等待时间，单位毫秒，未配置或者配置为 0 时，表示不设等待超时时间
spring.datasource.druid.max-wait=6000
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.validation-query-timeout=20
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=6000
spring.datasource.druid.min-evictable-idle-time-millis=150000
spring.datasource.druid.max-evictable-idle-time-millis=300000
################## 插件配置。 database config
spring.datasource.druid.filters=stat
# stat打开监控，统计慢sql
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.db-type=mysql
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=2000
# 应用将会有一个web页面，上面可以统计慢sql，每条sql的执行次数。 生产暂不配置，统一规划安全后统一启用
# stat-view-servlet

# mybatis plus
mybatis-plus.global-config.db-config.logic-delete-field=is_delete
mybatis-plus.global-config.db-config.logic-delete-value=Y
mybatis-plus.global-config.db-config.logic-not-delete-value=N
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis.type-aliases-package=com.winit.kms.client.handler.alias.**
##########################################  redis  ##################################################################
# Database
spring.redis.database=0
# Read timeout
spring.redis.timeout=60000
# Connect timeout
spring.redis.connectTimeout=2000
# Redis mode:sentinel,cluster
spring.redis.mode=cluster
# Sentinel
spring.redis.sentinel.master=
spring.redis.sentinel.nodes=
# Cluster
spring.redis.cluster.nodes=************:6380
# Node password
spring.redis.password=winit2023
# Redis pool
spring.redis.jedis.pool.maxActive=1000
spring.redis.jedis.pool.maxIdle=100
spring.redis.jedis.pool.minIdle=10
spring.redis.jedis.pool.maxWait=3000
# Hot key
spring.redis.hotKey.prefix=
spring.redis.hotKey.shards=3
# Cache prefix
spring.redis.cache.prefix=Cache-Cobra-agent:
distributed.lock.lockPrefix=Lock-Cobra-agent:


dubbo.scan.base-packages=com.winit.cobra.agent.spi.service,com.winit.gateway.dubbo.swagger.service
swagger.dubbo.enable=false
swagger.dubbo.web.enable=false

# 日志
#logback
logging.config=classpath:logback-spring.xml
#当前应用名称
dubbo.application.name=cobra-agent
#当前应用负责人
dubbo.application.owner=winit
#当前应用负责人组织
dubbo.application.organization=winit
#dubbo.registry.protocol=multicast
#dubbo.registry.address=*********:1234
#注册中心地址协议
dubbo.registry.protocol=zookeeper
#注册中心地址
dubbo.registry.address=************:2181
#dubbo.registry.address=***********:2181,***********:2181
#服务注册类型  false：静态类型(监控中心管理上下线) true:自动管理
dubbo.registry.dynamic=true
#服务协议 dubbo、rmi、hessian、http、webservice、thrift、memcached、redis
#缺省配置为dubbo协议，该采用单一长连接和NIO异步通讯，适合于小数据量大并发的服务调用，以及服务消费者机器数远大于服务提供者机器数的情况。
dubbo.protocol.name=dubbo
#服务数据通信端口
dubbo.protocol.port=20812
#all 所有消息都派发到线程池，包括请求，响应，连接事件，断开事件，心跳等。
#direct 所有消息都不派发到线程池，全部在IO线程上直接执行。
#message 只有请求响应消息派发到线程池，其它连接断开事件，心跳等消息，直接在IO线程上执行。
#execution 只请求消息派发到线程池，不含响应，响应和其它连接断开事件，心跳等消息，直接在IO线程上执行。
#connection 在IO线程上，将连接断开事件放入队列，有序逐个执行，其它消息派发到线程池。
dubbo.protocol.dispatcher=all
#fixed 固定大小线程池，启动时建立线程，不关闭，一直持有。(缺省)
#cached 缓存线程池，空闲一分钟自动删除，需要时重建。
#limited 可伸缩线程池，但池中的线程数只会增长不会收缩。(为避免收缩时突然来了大流量引起的性能问题)。
dubbo.protocol.threadpool=limited
#初始化线程池数量
dubbo.protocol.threads=200
dubbo.provider.version=3.0.0
#provider服务超时(建立provider端设置，consumer端不建议设置)
dubbo.provider.timeout=60000
#provider是否验证参数
dubbo.provider.validation=true
#provider并发数
dubbo.provider.executes=500
#集群模式 failover:失败自动切换(默认)、ailfast:一次调用失败、failsafe:失败安全、failback:失败恢复定时重发、broadcast:广播调用一台失败即失败
dubbo.provider.cluster=failover
#失败重试次数
dubbo.provider.retries=2
#负载平衡  random:随机、roundrobin：轮循、leastactive：最少活跃调用数、consistenthash：一致hash值
dubbo.provider.loadbalance=random

dubbo.apidoc.contextPath=/${dubbo.application.name}/dubbo

#whttp
dubbo.protocols.whttp.name=whttp
dubbo.protocols.whttp.port=${server.port}
dubbo.protocols.whttp.threads=200
dubbo.protocols.whttp.dispatcher=all
dubbo.protocols.whttp.server=wservlet

#consumer启动试不检查provider
dubbo.consumer.check=false

# spi version
dubbo.consumer.ums.version=3.0.0


# 健康检测
management.server.base-path=/${spring.application.name}
management.endpoints.web.base-path=/${spring.application.name}
management.endpoint.health.show-details=always
management.health.rabbit.enabled=false
#prometheus
management.endpoints.web.exposure.include=health,prometheus
#spring/tomcat/rabbitmq
management.metrics.enable.tomcat=false
management.metrics.enable.rabbitmq=false
#slo
management.metrics.distribution.slo[http.server.requests]=500ms,1s,3s,30s
management.metrics.distribution.slo[dubbo.server.requests]=500ms,1s,3s,30s
management.metrics.web.server.max-uri-tags=100
management.metrics.enable.http=true

spring.main.allow-bean-definition-overriding=true
spring.dubbo.provider.filter=dubboProviderPrometheusFilter,dubboProviderLogFilter
spring.dubbo.consumer.filter=dubboConsumerPrometheusFilter


########### kms ##########
kms.url=http://172.16.2.116:3990
# kms.keyId 为可选参数（目前只提供给pis使用,其他系统置为空值）
kms.keyId=4c8536de-0395-4146-9c6c-47c7c8aa594a
kms.groupId=oms
kms.retryTimes=3
kms.connectionTimeout=10000
kms.socketTimeout=15000
kms.enableEncrypt=true
kms.enableLocalKey=false
# kms.localPlainKey 测试环境使用，生产环境置为空值
kms.localPlainKey=QJKiNnWz8CKY09Cgd42DaVEFEW+up66jSLCUPYPMh6c=
kms.encryptionMode=AES-CBC



# focus 告警
alertpanel.enable=true
alertpanel.url=https://clinic.winit.com.cn/alert-panel
alertpanel.clientKey=cobra-agent
alertpanel.secret=7e12a437420547c8b69a73e4c69e1031

#需要告警的接口
api.failed.alert.api=v1.startConversation,v1.chatWithStream,v1.listTransformHumanChat,v1.chatFeedback,v1.chatDebugInfo,v1.queryChatDebugInfo,v1.transferToHuman,v1.cancelChat,com.winit.cobra.agent.spi.service.WorkflowService.run
#5分钟告警间隔
api.failed.alert.interval=5