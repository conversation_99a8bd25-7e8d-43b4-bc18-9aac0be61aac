01:06:21.368 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 1019870ms for sessionid 0x19439960e730498, closing socket connection and attempting reconnect
01:06:21.473 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
01:06:22.574 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:06:22.579 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:06:22.584 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
01:06:22.584 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730498 has expired, closing socket connection
01:06:22.584 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
01:06:22.585 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
01:06:22.585 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:06:22.585 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
01:06:22.590 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:06:22.596 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730499, negotiated timeout = 60000
01:06:22.596 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
01:06:30.955 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m9s882ms).
01:22:59.150 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m58s192ms).
01:22:59.544 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 948178ms for sessionid 0x19439960e730499, closing socket connection and attempting reconnect
01:22:59.655 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
01:23:01.096 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:23:01.101 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:23:01.106 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
01:23:01.106 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730499 has expired, closing socket connection
01:23:01.106 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
01:23:01.118 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:23:01.118 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
01:23:01.118 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
01:23:01.124 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:23:01.130 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73049a, negotiated timeout = 60000
01:23:01.131 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
01:42:06.481 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 976934ms for sessionid 0x19439960e73049a, closing socket connection and attempting reconnect
01:42:06.585 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
01:42:07.697 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:42:07.702 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:42:07.707 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
01:42:07.707 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e73049a has expired, closing socket connection
01:42:07.707 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
01:42:07.708 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:42:07.708 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
01:42:07.709 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
01:42:07.713 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:42:07.721 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73049b, negotiated timeout = 60000
01:42:07.721 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
01:42:26.114 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m26s946ms).
01:58:53.067 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m26s953ms).
01:58:53.425 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 976943ms for sessionid 0x19439960e73049b, closing socket connection and attempting reconnect
01:58:53.527 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
01:58:55.066 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:58:55.072 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:58:55.077 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
01:58:55.077 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e73049b has expired, closing socket connection
01:58:55.077 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
01:58:55.079 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
01:58:55.079 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
01:58:55.079 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
01:58:55.084 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
01:58:55.090 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73049c, negotiated timeout = 60000
01:58:55.090 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
