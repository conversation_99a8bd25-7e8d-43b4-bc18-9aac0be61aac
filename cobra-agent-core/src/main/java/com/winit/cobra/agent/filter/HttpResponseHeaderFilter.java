package com.winit.cobra.agent.filter;


import org.apache.http.HttpHeaders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * http 响应头部增强
 *
 * <AUTHOR>
 */
@Component
public class HttpResponseHeaderFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(HttpResponseHeaderFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        setConnection2Closed(request, response);
        filterChain.doFilter(request, response);
    }

    /**
     * 1、以HTTP方式提供服务的应用，流量切换的灰度状态下，在响应HTTP请求时要求客户端在接收到响应后立即关闭当前连接；
     * 2、不支持流量切换的应用，以java应用举例：可以通过接收shutdown指令后，在响应HTTP请求时要求客户端在接收到响应后立即关闭当前连接；
     *
     * @param request
     * @param response
     */
    private void setConnection2Closed(HttpServletRequest request, HttpServletResponse response) {
        response.setHeader(HttpHeaders.CONNECTION, "close");
        logger.info("当前是发版模式，统一将之前建立的长连接请求重置");

    }
}
