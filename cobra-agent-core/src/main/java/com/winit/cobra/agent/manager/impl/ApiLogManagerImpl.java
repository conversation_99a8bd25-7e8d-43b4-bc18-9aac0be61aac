package com.winit.cobra.agent.manager.impl;

import cn.hutool.core.builder.GenericBuilder;
import com.alibaba.fastjson.JSON;
import com.winit.cobra.agent.manager.ApiLogManager;
import com.winit.cobra.agent.manager.AsyncTaskManager;
import com.winit.cobra.agent.manager.SystemAlertManager;
import com.winit.cobra.agent.repository.mybatis.entity.ApiLogEntity;
import com.winit.cobra.agent.repository.mybatis.mapper.ApiLogMapper;
import com.winit.cobra.agent.spi.dto.request.RequestCommand;
import org.apache.dubbo.rpc.Invocation;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

import static com.winit.cobra.agent.constant.ChatModelCommonParam.FAILURE;
import static com.winit.cobra.agent.constant.ChatModelCommonParam.SUCCESS;

/**
 * api日志记录
 *
 * <AUTHOR>
 */
@Component
public class ApiLogManagerImpl implements ApiLogManager {

    @Resource
    private ApiLogMapper apiLogMapper;

    @Resource
    private AsyncTaskManager asyncTaskManager;

    @Resource
    private Executor apiLogTaskExecutor;

    @Resource
    private SystemAlertManager systemAlertManager;

    @Override
    public void logApiInvoke(RequestCommand<?> request, Object response, long executionTime, String requestResult) {
        ApiLogEntity apiLogEntity = asyncLogApi(request, response, executionTime, requestResult);
        sendAlert4FailedRequest(request.getAction(), requestResult, apiLogEntity);
    }

    @Override
    public void logDubboInvoke(Invocation invocation, String response, long executionTime, String requestResult) {
        ApiLogEntity apiLogEntity = asyncDubboLogApi(invocation, response, executionTime, requestResult);
        sendAlert4FailedRequest(String.format("%s.%s", invocation.getServiceName(), invocation.getMethodName()),
                requestResult, apiLogEntity);
    }

    /**
     * 截取JSON字符串，防止数据过大
     *
     * @param obj 需要转换为JSON的对象
     * @return 截取后的JSON字符串
     */
    private String truncateJson(Object obj) {
        if (obj == null) {
            return "";
        }
        
        String jsonStr = JSON.toJSONString(obj);
        final int MAX_LENGTH = 10000; // 设置最大长度限制
        
        if (jsonStr.length() > MAX_LENGTH) {
            return jsonStr.substring(0, MAX_LENGTH) + "...（数据已截断）";
        }
        
        return jsonStr;
    }

    /**
     * 异步日志记录
     */
    private ApiLogEntity asyncLogApi(RequestCommand<?> request, Object response, long executionTime, String requestResult) {
        ApiLogEntity apiLogEntity = GenericBuilder.of(ApiLogEntity::new)
                .with(ApiLogEntity::setAction, request.getAction())
                .with(ApiLogEntity::setRequest, truncateJson(request))
                .with(ApiLogEntity::setResponse, truncateJson(response))
                .with(ApiLogEntity::setAppKey, request.getApp_key())
                .with(ApiLogEntity::setCreatedby, request.getApp_key())
                .with(ApiLogEntity::setUpdatedby, request.getApp_key())
                .with(ApiLogEntity::setExecutionTime, executionTime)
                .with(ApiLogEntity::setRequestResult, requestResult)
                .build();
        asyncTaskManager.runAsync(apiLogMapper::insert, apiLogEntity, apiLogTaskExecutor);
        return apiLogEntity;
    }

    /**
     * 异步日志记录
     */
    private ApiLogEntity asyncDubboLogApi(Invocation invocation, Object response,
                                          long executionTime, String requestResult) {
        ApiLogEntity apiLogEntity = GenericBuilder.of(ApiLogEntity::new)
                .with(ApiLogEntity::setAction, String.format("%s.%s", invocation.getServiceName(), invocation.getMethodName()))
                .with(ApiLogEntity::setRequest, truncateJson(invocation))
                .with(ApiLogEntity::setResponse, truncateJson(response))
                .with(ApiLogEntity::setAppKey, "Dubbo Invoked Ignore")
                .with(ApiLogEntity::setCreatedby, "Dubbo Invoked Api Logged")
                .with(ApiLogEntity::setUpdatedby, "Dubbo Invoked Api Logged")
                .with(ApiLogEntity::setExecutionTime, executionTime)
                .with(ApiLogEntity::setRequestResult, requestResult)
                .build();
        asyncTaskManager.runAsync(apiLogMapper::insert, apiLogEntity, apiLogTaskExecutor);
        return apiLogEntity;
    }

    /**
     * 发送异常请求告警信息
     *
     * @param api
     * @param requestResult
     * @param apiLogEntity
     */
    private void sendAlert4FailedRequest(String api, String requestResult, ApiLogEntity apiLogEntity) {
        if (FAILURE.equals(requestResult)) {
            systemAlertManager.sendAlert(
                    "API接口异常",
                    api,
                    String.format("接口%s调用异常，异常信息：{}", JSON.toJSONString(apiLogEntity)));
        } else if (SUCCESS.equals(requestResult)) {
            systemAlertManager.cleanAlert(
                    "API接口异常",
                    api,
                    String.format("接口%s调用异常，异常信息：{}", JSON.toJSONString(apiLogEntity)));
        }
    }

}
