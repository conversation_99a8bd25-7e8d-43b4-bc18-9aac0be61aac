19:20:19.105 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:20:19.108 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:20:19.138 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:20:19.211 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 90558 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:20:19.212 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:20:19.810 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:20:19.810 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:20:19.810 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:20:19.811 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:20:19.811 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:20:20.039 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
19:20:20.040 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
19:20:20.061 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
19:20:20.116 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:20:20.117 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:20:20.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:20:20.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:20:20.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:20:20.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:20:20.121 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:20:20.163 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:20:20.182 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:20:20.883 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:20:20.891 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:20:20.892 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:20:20.893 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:20:20.955 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:20:20.956 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1703 ms
19:20:21.774 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:20:21.781 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:20:21.786 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:20:21.795 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:20:21.801 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:20:21.808 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:20:21.811 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:20:21.851 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:20:21.864 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:20:21.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:20:21.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:20:21.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
19:20:21.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:20:21.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.33/fastjson2-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:20:21.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:20:21.871 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@6785786d
19:20:21.882 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:20:21.882 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:20:21.889 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:20:21.898 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73046a, negotiated timeout = 60000
19:20:21.901 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:20:28.117 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:20:29.969 [main] [] INFO  org.reflections.Reflections - Reflections took 63 ms to scan 1 urls, producing 2 keys and 2 values 
19:20:39.375 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:20:39.390 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:20:42.708 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 23.863 seconds (JVM running for 24.931)
19:20:43.110 [RMI TCP Connection(15)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:20:43.136 [RMI TCP Connection(14)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:20:43.136 [RMI TCP Connection(14)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:20:43.175 [RMI TCP Connection(14)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 39 ms
19:20:43.626 [RMI TCP Connection(15)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:20:43.720 [RMI TCP Connection(15)-127.0.0.1] [] WARN  o.s.b.a.redis.RedisHealthIndicator - Redis health check failed
org.springframework.data.redis.ClusterStateFailureException: Could not retrieve cluster information. CLUSTER NODES returned with error.
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection$JedisClusterTopologyProvider.getTopology(JedisClusterConnection.java:1056)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.getClusterTopology(ClusterCommandExecutor.java:327)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.executeCommandOnArbitraryNode(ClusterCommandExecutor.java:105)
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection.clusterGetClusterInfo(JedisClusterConnection.java:768)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:60)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:51)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:71)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:39)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:99)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:96)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:74)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:61)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:55)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor94.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:22:45.193 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:22:45.200 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e73046a closed
19:22:45.200 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:22:45.215 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:22:45.219 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:22:45.219 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@9f38e90 was destroying!
19:22:45.219 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:22:48.385 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:22:48.387 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:22:48.395 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:22:48.415 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 90653 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:22:48.415 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:22:48.789 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:22:48.790 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:22:48.790 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:22:48.790 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:22:48.790 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:22:48.921 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
19:22:48.922 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
19:22:48.935 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
19:22:48.974 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:22:48.974 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:22:48.974 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:22:48.974 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:22:48.974 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:22:48.974 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:22:48.977 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:22:49.005 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:22:49.020 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:22:49.360 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:22:49.364 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:22:49.365 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:22:49.365 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:22:49.406 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:22:49.406 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 972 ms
19:22:49.749 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:22:49.756 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:22:49.760 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:22:49.763 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:22:49.768 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:22:49.774 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:22:49.776 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:22:49.808 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:22:49.821 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.33/fastjson2-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:22:49.825 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@576323ff
19:22:49.832 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:22:49.833 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:22:49.837 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:22:49.847 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73046b, negotiated timeout = 60000
19:22:49.850 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:22:56.230 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:22:58.242 [main] [] INFO  org.reflections.Reflections - Reflections took 72 ms to scan 1 urls, producing 2 keys and 2 values 
19:23:07.600 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:23:07.616 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:23:10.834 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 22.583 seconds (JVM running for 23.053)
19:23:11.128 [RMI TCP Connection(14)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:23:11.154 [RMI TCP Connection(15)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:23:11.155 [RMI TCP Connection(15)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:23:11.195 [RMI TCP Connection(15)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 40 ms
19:23:11.678 [RMI TCP Connection(14)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:23:11.771 [RMI TCP Connection(14)-127.0.0.1] [] WARN  o.s.b.a.redis.RedisHealthIndicator - Redis health check failed
org.springframework.data.redis.ClusterStateFailureException: Could not retrieve cluster information. CLUSTER NODES returned with error.
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection$JedisClusterTopologyProvider.getTopology(JedisClusterConnection.java:1056)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.getClusterTopology(ClusterCommandExecutor.java:327)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.executeCommandOnArbitraryNode(ClusterCommandExecutor.java:105)
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection.clusterGetClusterInfo(JedisClusterConnection.java:768)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:60)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:51)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:71)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:39)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:99)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:96)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:74)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:61)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:55)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor94.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:23:30.585 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"和特斯拉有啥关系","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:23:31.008 [http-nio-9090-exec-1] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:23:32.023 [http-nio-9090-exec-1] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
### The error may exist in com/winit/cobra/agent/repository/mybatis/mapper/ConversationMapper.java (best guess)
### The error may involve com.winit.cobra.agent.repository.mybatis.mapper.ConversationHistoryMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO cobra_conversation_history  ( id, user_id, mode_name, model_conversation_id )  VALUES (  ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
### The error may exist in com/winit/cobra/agent/repository/mybatis/mapper/ConversationMapper.java (best guess)
### The error may involve com.winit.cobra.agent.repository.mybatis.mapper.ConversationHistoryMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO cobra_conversation_history  ( id, user_id, mode_name, model_conversation_id )  VALUES (  ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy124.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy129.insert(Unknown Source)
	at com.winit.cobra.agent.manager.impl.ConversationManagerImpl.saveConversation(ConversationManagerImpl.java:36)
	at com.winit.cobra.agent.service.ChatServiceImpl.fireConversationId(ChatServiceImpl.java:214)
	at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:75)
	at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	at com.winit.cobra.agent.controller.ChatController$$FastClassBySpringCGLIB$$715bfcc0.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$bc65479c.chatWithStream(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.SQLSyntaxErrorException: Table 'ai_chat.cobra_conversation_history' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 109 common frames omitted
19:24:23.230 [http-nio-9090-exec-2] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"和特斯拉有啥关系","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:24:23.248 [http-nio-9090-exec-2] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:24:54.588 [http-nio-9090-exec-4] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"你好","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:24:54.594 [http-nio-9090-exec-4] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:25:43.107 [http-nio-9090-exec-6] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:25:43.122 [http-nio-9090-exec-6] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:26:13.148 [http-nio-9090-exec-8] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:26:13.155 [http-nio-9090-exec-8] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:28:05.380 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:28:05.389 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:28:05.389 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e73046b closed
19:28:05.420 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:28:05.426 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:28:05.428 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@6874bfd9 was destroying!
19:28:05.428 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:28:09.778 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:28:09.781 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:28:09.811 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:28:09.838 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 90882 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:28:09.839 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:28:10.352 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:28:10.353 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:28:10.353 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:28:10.353 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:28:10.353 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:28:10.483 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
19:28:10.485 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
19:28:10.497 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
19:28:10.533 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:28:10.534 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:28:10.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:28:10.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:28:10.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:28:10.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:28:10.536 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:28:10.564 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:28:10.581 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:28:10.919 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:28:10.923 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:28:10.924 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:28:10.924 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:28:10.966 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:28:10.966 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1100 ms
19:28:11.295 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" hostname="ganyi.local" organization="winit" qosEnable="false" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:28:11.300 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:28:11.304 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:28:11.306 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:28:11.310 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:28:11.316 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:28:11.318 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:28:11.348 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:28:11.360 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:28:11.363 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.33/fastjson2-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:28:11.364 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
19:28:11.371 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:28:11.371 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:28:11.377 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:28:11.384 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73046c, negotiated timeout = 60000
19:28:11.386 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:28:12.228 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:28:12.324 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
19:28:12.725 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:28:12.729 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:28:12.878 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.364 seconds (JVM running for 4.585)
19:28:13.247 [RMI TCP Connection(3)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:28:13.247 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:28:13.248 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:28:13.250 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
19:28:13.381 [RMI TCP Connection(3)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:28:13.395 [RMI TCP Connection(3)-127.0.0.1] [] WARN  o.s.b.a.redis.RedisHealthIndicator - Redis health check failed
org.springframework.data.redis.ClusterStateFailureException: Could not retrieve cluster information. CLUSTER NODES returned with error.
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection$JedisClusterTopologyProvider.getTopology(JedisClusterConnection.java:1056)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.getClusterTopology(ClusterCommandExecutor.java:327)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.executeCommandOnArbitraryNode(ClusterCommandExecutor.java:105)
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection.clusterGetClusterInfo(JedisClusterConnection.java:768)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:60)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:51)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:71)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:39)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:99)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:96)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:74)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:61)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:55)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor88.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:28:18.670 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:28:18.718 [http-nio-9090-exec-1] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:38:12.713 [http-nio-9090-exec-4] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:38:12.720 [http-nio-9090-exec-4] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:39:56.348 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:39:56.355 [http-nio-9090-exec-7] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:40:20.174 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:40:20.181 [http-nio-9090-exec-9] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
19:41:24.667 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m11s104ms).
19:41:24.689 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 72840ms for sessionid 0x19439960e73046c, closing socket connection and attempting reconnect
19:41:24.795 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
19:41:26.598 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:41:26.609 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:41:26.616 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
19:41:26.616 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e73046c has expired, closing socket connection
19:41:26.618 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
19:41:26.619 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:41:26.619 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
19:41:26.619 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:41:26.623 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:41:26.632 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730470, negotiated timeout = 60000
19:41:26.632 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
19:45:22.492 [http-nio-9090-exec-2] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
19:45:22.498 [http-nio-9090-exec-2] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
