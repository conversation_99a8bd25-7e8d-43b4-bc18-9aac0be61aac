package com.winit.cobra.agent.controller;

import com.winit.cobra.agent.constant.ApiErrorCode;
import com.winit.cobra.agent.exception.ApiException;
import com.winit.cobra.agent.spi.dto.request.RequestCommand;
import com.winit.cobra.agent.spi.dto.request.RunWorkflowCommand;
import com.winit.cobra.agent.spi.dto.response.RunWorkflowVo;
import com.winit.cobra.agent.spi.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 工作流管理接口
 */
@Api(tags = {"工作流管理"})
@RestController
@CrossOrigin
public class WorkflowController extends BaseV1Controller {

    public static final Logger logger = LoggerFactory.getLogger(WorkflowController.class);

    @Resource
    private WorkflowService workflowService;

    /**
     * 执行工作流
     *
     * @param command 工作流执行请求
     * @return 工作流执行结果
     */
    @ApiOperation(value = "执行工作流", notes = "执行工作流", httpMethod = "POST")
    @PostMapping("/workflow/run")
    public RunWorkflowVo run(
            @ApiParam(value = "工作流执行请求", required = true)
            @RequestBody @Validated RequestCommand<RunWorkflowCommand> command) {

        logger.info("收到工作流执行请求, 工作流ID: {}",
                command.getData().getWorkflowId());

        try {
            RunWorkflowVo result = workflowService.run(command.getData());
            logger.info("工作流执行成功， 工作流ID: {}, 执行ID: {}",
                    command.getData().getWorkflowId(),
                    result.getExecuteID());
            return result;
        } catch (Exception e) {
            logger.error("工作流执行失败 工作流ID: {}, 错误信息: {}",
                    command.getData().getWorkflowId(),
                    e.getMessage(), e);
            throw new ApiException(ApiErrorCode.SERVICE_ACCESS_EXCEPTION);
        }
    }
}
