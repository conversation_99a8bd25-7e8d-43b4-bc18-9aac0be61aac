<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.winit</groupId>
  <artifactId>cobra-agent</artifactId>
  <version>3.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>cobra-agent</name>
  <description>Winit AI Agent</description>
  <url>https://spring.io/projects/spring-boot/cobra-agent</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Pivotal</name>
      <email><EMAIL></email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/spring-projects/spring-boot/cobra-agent</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>releases</id>
      <name>Nexus Repository</name>
      <url>http://***********:8081/nexus/content/repositories/releases</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>Nexus Repository</name>
      <url>http://***********:8081/nexus/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>winit</id>
      <url>http://***********:8081/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <id>aliyun</id>
      <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
  </repositories>
</project>
