17:44:17.689 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

17:44:17.693 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
17:44:17.712 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
17:44:17.735 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 56603 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
17:44:17.735 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
17:44:18.095 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
17:44:18.095 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
17:44:18.095 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
17:44:18.096 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
17:44:18.096 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
17:44:18.238 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
17:44:18.239 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
17:44:18.255 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
17:44:18.301 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:18.301 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
17:44:18.301 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:18.301 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:18.301 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:18.301 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:18.305 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
17:44:18.342 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
17:44:18.362 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
17:44:19.172 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
17:44:19.176 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
17:44:19.177 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
17:44:19.177 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
17:44:19.220 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
17:44:19.220 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1465 ms
17:44:19.577 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
17:44:19.582 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
17:44:19.586 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
17:44:19.588 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
17:44:19.593 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
17:44:19.598 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
17:44:19.600 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
17:44:19.629 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
17:44:19.640 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
17:44:19.645 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@394950ad
17:44:19.652 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
17:44:19.652 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
17:44:19.657 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
17:44:19.664 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73050b, negotiated timeout = 60000
17:44:19.666 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
17:44:20.472 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
17:44:20.565 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
17:44:20.909 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
17:44:20.918 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
17:44:21.059 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.518 seconds (JVM running for 4.63)
17:44:21.289 [RMI TCP Connection(1)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
17:44:21.290 [RMI TCP Connection(4)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:44:21.290 [RMI TCP Connection(4)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
17:44:21.291 [RMI TCP Connection(4)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
17:44:21.411 [RMI TCP Connection(1)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
17:44:21.426 [RMI TCP Connection(1)-127.0.0.1] [] WARN  o.s.b.a.redis.RedisHealthIndicator - Redis health check failed
org.springframework.data.redis.ClusterStateFailureException: Could not retrieve cluster information. CLUSTER NODES returned with error.
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection$JedisClusterTopologyProvider.getTopology(JedisClusterConnection.java:1056)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.getClusterTopology(ClusterCommandExecutor.java:327)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.executeCommandOnArbitraryNode(ClusterCommandExecutor.java:105)
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection.clusterGetClusterInfo(JedisClusterConnection.java:768)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:60)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:51)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:71)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:39)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:99)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:96)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:74)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:61)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:55)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor88.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
17:44:27.380 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","data":{"chatId":"","conversationId":"7472968095703760947","message":"echo_hi","toolCallOut":[],"toolContext":{"in":"test"},"userId":"chengcheng.qin","username":"zhangsan"},"format":"json","language":"zh_CN","platform":"iam","sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
17:44:27.509 [http-nio-9090-exec-1] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - 03010250006
com.winit.cobra.agent.exception.ApiException: 03010250006
	at com.winit.cobra.agent.auth.SignCheckHandler.checkUserExist(SignCheckHandler.java:58)
	at com.winit.cobra.agent.auth.SignCheckHandler.handle(SignCheckHandler.java:34)
	at com.winit.cobra.agent.auth.SignCheckHandler.handle(SignCheckHandler.java:22)
	at com.winit.cobra.agent.auth.ApiRequestAuthAspect.verify(ApiRequestAuthAspect.java:61)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:617)
	at org.springframework.aop.aspectj.AspectJMethodBeforeAdvice.before(AspectJMethodBeforeAdvice.java:44)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$4db7b7cb.chatWithStream(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
17:44:40.468 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
17:44:40.474 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e73050b closed
17:44:40.474 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
17:44:40.493 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
17:44:40.497 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
17:44:40.497 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@323631af was destroying!
17:44:40.497 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
17:44:43.387 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

17:44:43.389 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
17:44:43.399 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
17:44:43.417 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 56643 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
17:44:43.417 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
17:44:43.782 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
17:44:43.782 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
17:44:43.783 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
17:44:43.783 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
17:44:43.783 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
17:44:43.917 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
17:44:43.918 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
17:44:43.931 [main] [] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
17:44:43.969 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:43.969 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
17:44:43.969 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:43.970 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:43.970 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:43.970 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
17:44:43.972 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
17:44:43.999 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
17:44:44.012 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
17:44:44.347 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
17:44:44.352 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
17:44:44.353 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
17:44:44.353 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
17:44:44.401 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
17:44:44.401 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 962 ms
17:44:44.797 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
17:44:44.803 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
17:44:44.807 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
17:44:44.809 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
17:44:44.814 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
17:44:44.820 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
17:44:44.823 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
17:44:44.853 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
17:44:44.865 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/com/winit/cache-spring-boot-starter/1.0.3-SNAPSHOT/cache-spring-boot-starter-1.0.3-20230818.084601-3.jar:/Users/<USER>/.m2/repository/com/winit/cache-redis-v2/1.0.3-SNAPSHOT/cache-redis-v2-1.0.3-20230818.084600-3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.4.9/spring-data-redis-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.4.9/spring-data-keyvalue-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.4.9/spring-data-commons-2.4.9.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.7/spring-oxm-5.3.7.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.3.0/jedis-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
17:44:44.869 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
17:44:44.870 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@135a8808
17:44:44.877 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
17:44:44.877 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
17:44:44.882 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
17:44:44.891 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73050c, negotiated timeout = 60000
17:44:44.894 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
17:44:45.714 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
17:44:45.805 [main] [] INFO  org.reflections.Reflections - Reflections took 5 ms to scan 1 urls, producing 2 keys and 2 values 
17:44:46.163 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
17:44:46.167 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
17:44:46.354 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.109 seconds (JVM running for 3.665)
17:44:46.476 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:44:46.477 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
17:44:46.477 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
17:44:46.479 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
17:44:46.557 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","data":{"chatId":"","conversationId":"7472968095703760947","message":"echo_hi","toolCallOut":[],"toolContext":{"in":"test"},"userId":"chengcheng.qin","username":"zhangsan"},"format":"json","language":"zh_CN","platform":"iam","sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
17:44:46.633 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
17:44:46.670 [RMI TCP Connection(4)-127.0.0.1] [] WARN  o.s.b.a.redis.RedisHealthIndicator - Redis health check failed
org.springframework.data.redis.ClusterStateFailureException: Could not retrieve cluster information. CLUSTER NODES returned with error.
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection$JedisClusterTopologyProvider.getTopology(JedisClusterConnection.java:1056)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.getClusterTopology(ClusterCommandExecutor.java:327)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.executeCommandOnArbitraryNode(ClusterCommandExecutor.java:105)
	at org.springframework.data.redis.connection.jedis.JedisClusterConnection.clusterGetClusterInfo(JedisClusterConnection.java:768)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:60)
	at org.springframework.boot.actuate.redis.RedisHealthIndicator.doHealthCheck(RedisHealthIndicator.java:51)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:71)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:39)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:99)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:96)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:74)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:61)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:55)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor87.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
17:44:54.144 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","data":{"chatId":"","conversationId":"7472968095703760947","message":"echo_hi","toolCallOut":[],"toolContext":{"in":"test"},"userId":"chengcheng.qin","username":"zhangsan"},"format":"json","language":"zh_CN","platform":"iam","sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
17:46:15.461 [http-nio-9090-exec-6] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","data":{"chatId":"","conversationId":"7472968095703760947","message":"echo_hi","toolCallOut":[],"toolContext":{"in":"test"},"userId":"chengcheng.qin","username":"zhangsan"},"format":"json","language":"zh_CN","platform":"iam","sign":"01B9B209D417DD6EB1F2BCFF9EEA7911","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
17:59:56.076 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to read additional data from server sessionid 0x19439960e73050c, likely server has closed socket, closing socket connection and attempting reconnect
17:59:56.206 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
17:59:57.481 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
17:59:57.569 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
17:59:57.583 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
17:59:57.584 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e73050c has expired, closing socket connection
17:59:57.585 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@135a8808
17:59:57.587 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
17:59:57.587 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
17:59:57.587 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
17:59:57.593 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
17:59:57.601 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73050d, negotiated timeout = 60000
17:59:57.601 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
