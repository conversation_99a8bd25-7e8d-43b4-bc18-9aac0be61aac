16:00:03.564 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

16:00:03.566 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
16:00:03.575 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
16:00:03.594 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 89663 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
16:00:03.594 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
16:00:03.906 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
16:00:03.906 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
16:00:03.906 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
16:00:03.906 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
16:00:03.906 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
16:00:04.037 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:00:04.037 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
16:00:04.038 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:00:04.038 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:00:04.038 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:00:04.038 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:00:04.040 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
16:00:04.068 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
16:00:04.084 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
16:00:04.403 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
16:00:04.407 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
16:00:04.408 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
16:00:04.408 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
16:00:04.445 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
16:00:04.446 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 834 ms
16:00:04.762 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
16:00:04.767 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
16:00:04.771 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
16:00:04.774 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
16:00:04.781 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
16:00:04.787 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
16:00:04.790 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
16:00:04.821 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
16:00:04.833 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.33/fastjson2-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
16:00:04.838 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@43dddfdd
16:00:04.845 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
16:00:04.846 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
16:00:04.854 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
16:00:04.863 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730372, negotiated timeout = 60000
16:00:04.865 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
16:00:05.573 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
16:00:05.661 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
16:00:05.952 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
16:00:05.957 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
16:00:06.088 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 2.656 seconds (JVM running for 3.154)
16:00:06.146 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:00:06.146 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
16:00:06.147 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:00:06.147 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
16:00:06.262 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
16:00:12.333 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"say_hi","username":"zhangsan","userId":"chengcheng.qin","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:00:12.376 [http-nio-9090-exec-1] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
16:00:27.868 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"say_hi","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469697215364071459","chatId":"7469697222297518119","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJQxZEEEZGSkFeEUFFEl5HEUYRXhJKQRBeEUtBQxZHR0dEEksQSUI=","output":"{\"out\":\"你好 北京欢迎你\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:00:29.938 [OkHttp https://api.coze.cn/...] [] INFO  c.w.c.agent.coze.model.CozeChatModel - Chat event completed, Conversation: 7469697215364071459,chat:7469697222297518119, Usage: ChatUsage(tokenCount=324, outputCount=73, inputCount=251)
16:04:20.801 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
16:04:20.809 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730372 closed
16:04:20.809 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
16:04:20.826 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
16:04:20.830 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
16:04:20.831 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@28a18fab was destroying!
16:04:20.831 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
16:04:40.529 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

16:04:40.530 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
16:04:40.540 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
16:04:40.558 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.13 on ganyi.local with PID 90079 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
16:04:40.558 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
16:04:40.902 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
16:04:40.902 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
16:04:40.902 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
16:04:40.903 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
16:04:40.903 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
16:04:41.035 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:04:41.035 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
16:04:41.035 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:04:41.035 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:04:41.035 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:04:41.035 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
16:04:41.041 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
16:04:41.071 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
16:04:41.084 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
16:04:41.420 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
16:04:41.425 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
16:04:41.425 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
16:04:41.425 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
16:04:41.465 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
16:04:41.465 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 890 ms
16:04:41.780 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
16:04:41.785 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
16:04:41.789 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
16:04:41.791 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
16:04:41.795 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
16:04:41.801 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
16:04:41.803 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
16:04:41.833 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
16:04:41.844 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.13
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.13/Contents/Home
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.33/fastjson2-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
16:04:41.848 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
16:04:41.849 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@5693d1d2
16:04:41.860 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
16:04:41.861 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
16:04:41.870 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
16:04:41.890 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730373, negotiated timeout = 60000
16:04:41.893 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
16:04:42.602 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
16:04:42.685 [main] [] INFO  org.reflections.Reflections - Reflections took 5 ms to scan 1 urls, producing 2 keys and 2 values 
16:04:42.989 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
16:04:42.992 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
16:04:43.119 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 2.713 seconds (JVM running for 3.158)
16:04:43.230 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
16:04:43.234 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:04:43.235 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
16:04:43.236 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
16:04:43.350 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
16:04:55.744 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"你是谁可以做什么","username":"zhangsan","userId":"chengcheng.qin","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:04:55.791 [http-nio-9090-exec-1] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
16:06:15.999 [http-nio-9090-exec-4] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"我们玩个游戏，当有人想你问好的时候，你要调用 指令 say_hi，然后将调用开始时候的对话指令结果拼接到每次对话的末尾","username":"zhangsan","userId":"chengcheng.qin","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:06:16.004 [http-nio-9090-exec-4] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
16:06:44.777 [http-nio-9090-exec-6] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"我们玩个游戏，当有人想你问好的时候，你要调用 指令 say_hi，然后将调用开始时候的对话指令结果拼接到每次对话的末尾","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469698764903743538","chatId":"7469698780988801058","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJQxZEEEZGSkFeEUFFEl5HEUYRXhJKQRBeEUtBQxZHR0dEEksQSUI=","output":"{\"out\":\"你好 这是结果1\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:06:45.160 [OkHttp https://api.coze.cn/...] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4000,"msg":"Request parameter error"}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxSource] :
	reactor.core.publisher.Flux.from(Flux.java:1088)
	com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:454)
Error has been observed at the following site(s):
	|_          Flux.from ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:454)
	|_      Flux.doOnNext ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:455)
	|_        Flux.filter ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:463)
	|_           Flux.map ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:464)
	|_       Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_     Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_     Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_  Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_ Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_      Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_  Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:06:45.162 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4000,"msg":"Request parameter error"}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxSource] :
	reactor.core.publisher.Flux.from(Flux.java:1088)
	com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:454)
Error has been observed at the following site(s):
	|_            Flux.from ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:454)
	|_        Flux.doOnNext ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:455)
	|_          Flux.filter ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:463)
	|_             Flux.map ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:464)
	|_         Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_       Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_       Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_    Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:125)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:06:45.169 [http-nio-9090-exec-7] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4000,"msg":"Request parameter error"}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4000,"msg":"Request parameter error"}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxSource] :
	reactor.core.publisher.Flux.from(Flux.java:1088)
	com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:454)
Error has been observed at the following site(s):
	|_            Flux.from ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:454)
	|_        Flux.doOnNext ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:455)
	|_          Flux.filter ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:463)
	|_             Flux.map ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:464)
	|_         Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_       Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_       Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_    Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:125)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:140)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:07:06.563 [http-nio-9090-exec-8] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"我们玩个游戏，当有人想你问好的时候，你要调用 指令 say_hi，然后将调用开始时候的对话指令结果拼接到每次对话的末尾","username":"zhangsan","userId":"chengcheng.qin","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:07:06.569 [http-nio-9090-exec-8] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
16:07:20.301 [http-nio-9090-exec-10] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"我们玩个游戏，当有人想你问好的时候，你要调用 指令 say_hi，然后将调用开始时候的对话指令结果拼接到每次对话的末尾","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469698993040212004","chatId":"7469698997058453538","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJFURBFRJAEBFeEUUVSl5HRkVHXhFGEUJeQRZHQ0QXQkRFShZBSUI=","output":"{\"out\":\"你好 这是结果1\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:07:26.349 [OkHttp https://api.coze.cn/...] [] INFO  c.w.c.agent.coze.model.CozeChatModel - Chat event completed, Conversation: 7469698993040212004,chat:7469698997058453538, Usage: ChatUsage(tokenCount=901, outputCount=307, inputCount=594)
16:07:43.221 [http-nio-9090-exec-2] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"为什么呢","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469698993040212004","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJFURBFRJAEBFeEUUVSl5HRkVHXhFGEUJeQRZHQ0QXQkRFShZBSUI=","output":"{\"out\":\"你好 这是结果1\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:07:44.858 [http-nio-9090-exec-2] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
java.lang.NullPointerException: chatID is marked non-null but is null
	at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxFlatMap] :
	reactor.core.publisher.Flux.flatMap(Flux.java:5014)
	com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
Error has been observed at the following site(s):
	|_       Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_     Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_     Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_  Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_ Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_      Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_  Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
		at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:448)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireToolCalls(CozeChatModel.java:288)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireModelChatStream(CozeChatModel.java:179)
		at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$3(CozeChatModel.java:148)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:152)
		at reactor.core.publisher.FluxFlatMap.subscribeOrReturn(FluxFlatMap.java:93)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxPublishOn.subscribeOrReturn(FluxPublishOn.java:92)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8235)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$DeferredResultSubscriber.connect(ReactiveTypeHandler.java:451)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler.handleValue(ReactiveTypeHandler.java:166)
		at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler.handleReturnValue(ResponseBodyEmitterReturnValueHandler.java:154)
		at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
		at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
		at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
		at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
		at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:07:44.861 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
java.lang.NullPointerException: chatID is marked non-null but is null
	at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxFlatMap] :
	reactor.core.publisher.Flux.flatMap(Flux.java:5014)
	com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
Error has been observed at the following site(s):
	|_         Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_       Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_       Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_    Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:125)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
		at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:448)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireToolCalls(CozeChatModel.java:288)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireModelChatStream(CozeChatModel.java:179)
		at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$3(CozeChatModel.java:148)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:152)
		at reactor.core.publisher.FluxFlatMap.subscribeOrReturn(FluxFlatMap.java:93)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxPublishOn.subscribeOrReturn(FluxPublishOn.java:92)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8235)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$DeferredResultSubscriber.connect(ReactiveTypeHandler.java:451)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler.handleValue(ReactiveTypeHandler.java:166)
		at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler.handleReturnValue(ResponseBodyEmitterReturnValueHandler.java:154)
		at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
		at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
		at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
		at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
		at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:07:44.867 [http-nio-9090-exec-3] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - chatID is marked non-null but is null
java.lang.NullPointerException: chatID is marked non-null but is null
	at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxFlatMap] :
	reactor.core.publisher.Flux.flatMap(Flux.java:5014)
	com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
Error has been observed at the following site(s):
	|_         Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_       Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_       Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_    Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:125)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:140)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
		at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:448)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireToolCalls(CozeChatModel.java:288)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireModelChatStream(CozeChatModel.java:179)
		at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$3(CozeChatModel.java:148)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:152)
		at reactor.core.publisher.FluxFlatMap.subscribeOrReturn(FluxFlatMap.java:93)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxPublishOn.subscribeOrReturn(FluxPublishOn.java:92)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8235)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$DeferredResultSubscriber.connect(ReactiveTypeHandler.java:451)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler.handleValue(ReactiveTypeHandler.java:166)
		at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler.handleReturnValue(ResponseBodyEmitterReturnValueHandler.java:154)
		at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
		at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
		at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
		at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
		at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:07:57.079 [http-nio-9090-exec-4] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"为什么呢","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469698993040212004","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:08:33.443 [http-nio-9090-exec-6] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"那你之前告诉我执行的指令是哪个你还记得吗","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469698993040212004","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:09:40.313 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"你好，需要你帮我做个事情，每次执行完，都将执行指令拼接到返回的内容末尾 sayhi","username":"zhangsan","userId":"chengcheng.qin","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:09:40.318 [http-nio-9090-exec-9] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
16:09:54.629 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"你好，需要你帮我做个事情，每次执行完，都将执行指令拼接到返回的内容末尾 say_hi","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:10:09.417 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"你好，需要你帮我做个事情，每次执行完，都将执行指令拼接到返回的内容末尾 say_hi","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","chatId":"7469699719502282786","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJFxYQF0JKEUReSxVKEF5HEUsSXkpGQ0BeR0FFFkNEQkIVREpBSUI=","output":"{\"out\":\"你好 这是结果1\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:10:11.762 [OkHttp https://api.coze.cn/...] [] INFO  c.w.c.agent.coze.model.CozeChatModel - Chat event completed, Conversation: 7469699646986862626,chat:7469699719502282786, Usage: ChatUsage(tokenCount=405, outputCount=98, inputCount=307)
16:10:34.169 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"百度是一家什么公司呢？","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJFxYQF0JKEUReSxVKEF5HEUsSXkpGQ0BeR0FFFkNEQkIVREpBSUI=","output":"{\"out\":\"你好 这是结果1\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:10:35.806 [http-nio-9090-exec-5] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
java.lang.NullPointerException: chatID is marked non-null but is null
	at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxFlatMap] :
	reactor.core.publisher.Flux.flatMap(Flux.java:5014)
	com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
Error has been observed at the following site(s):
	|_       Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_     Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_     Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_  Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_ Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_      Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_  Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
		at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:448)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireToolCalls(CozeChatModel.java:288)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireModelChatStream(CozeChatModel.java:179)
		at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$3(CozeChatModel.java:148)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:152)
		at reactor.core.publisher.FluxFlatMap.subscribeOrReturn(FluxFlatMap.java:93)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxPublishOn.subscribeOrReturn(FluxPublishOn.java:92)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8235)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$DeferredResultSubscriber.connect(ReactiveTypeHandler.java:451)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler.handleValue(ReactiveTypeHandler.java:166)
		at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler.handleReturnValue(ResponseBodyEmitterReturnValueHandler.java:154)
		at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
		at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
		at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
		at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
		at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:10:35.808 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
java.lang.NullPointerException: chatID is marked non-null but is null
	at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxFlatMap] :
	reactor.core.publisher.Flux.flatMap(Flux.java:5014)
	com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
Error has been observed at the following site(s):
	|_         Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_       Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_       Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_    Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:125)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
		at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:448)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireToolCalls(CozeChatModel.java:288)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireModelChatStream(CozeChatModel.java:179)
		at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$3(CozeChatModel.java:148)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:152)
		at reactor.core.publisher.FluxFlatMap.subscribeOrReturn(FluxFlatMap.java:93)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxPublishOn.subscribeOrReturn(FluxPublishOn.java:92)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8235)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$DeferredResultSubscriber.connect(ReactiveTypeHandler.java:451)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler.handleValue(ReactiveTypeHandler.java:166)
		at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler.handleReturnValue(ResponseBodyEmitterReturnValueHandler.java:154)
		at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
		at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
		at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
		at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
		at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:10:35.811 [http-nio-9090-exec-6] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - chatID is marked non-null but is null
java.lang.NullPointerException: chatID is marked non-null but is null
	at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxFlatMap] :
	reactor.core.publisher.Flux.flatMap(Flux.java:5014)
	com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
Error has been observed at the following site(s):
	|_         Flux.flatMap ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:148)
	|_       Flux.doOnError ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:149)
	|_       Flux.doFinally ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:150)
	|_    Flux.contextWrite ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$6(CozeChatModel.java:151)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:125)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:140)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.SubmitToolOutputsReq$SubmitToolOutputsReqBuilder.chatID(SubmitToolOutputsReq.java:18)
		at com.winit.cobra.agent.coze.model.CozeChatModel.submitStreamToolCallOut(CozeChatModel.java:448)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireToolCalls(CozeChatModel.java:288)
		at com.winit.cobra.agent.coze.model.CozeChatModel.fireModelChatStream(CozeChatModel.java:179)
		at com.winit.cobra.agent.coze.model.CozeChatModel.lambda$stream$3(CozeChatModel.java:148)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:152)
		at reactor.core.publisher.FluxFlatMap.subscribeOrReturn(FluxFlatMap.java:93)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxPublishOn.subscribeOrReturn(FluxPublishOn.java:92)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:55)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDeferContextual.subscribe(FluxDeferContextual.java:57)
		at reactor.core.publisher.Flux.subscribe(Flux.java:8235)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler$DeferredResultSubscriber.connect(ReactiveTypeHandler.java:451)
		at org.springframework.web.servlet.mvc.method.annotation.ReactiveTypeHandler.handleValue(ReactiveTypeHandler.java:166)
		at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler.handleReturnValue(ResponseBodyEmitterReturnValueHandler.java:154)
		at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
		at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
		at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
		at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
		at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:840)
16:10:41.200 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"百度是一家什么公司呢？","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:11:07.445 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"你还记得最后一次返回的指令是哪个吗","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:11:42.673 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"那把这个指令的每次结果从现在开始帮我追加到每次对话结尾可以吗？","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:12:04.155 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"那把这个指令的每次结果从现在开始帮我追加到每次对话结尾可以吗？","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","chatId":"7469700183207575579","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[{"toolCallId":"BUJJShUXQEVDQkFeRBASSl5HRhJLXhFLREpeEkISRRdLShEXQkNBSUI=","output":"{\"out\":\"你好 这是结果3\"}","functionName":"say_hi"}]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:12:06.854 [OkHttp https://api.coze.cn/...] [] INFO  c.w.c.agent.coze.model.CozeChatModel - Chat event completed, Conversation: 7469699646986862626,chat:7469700183207575579, Usage: ChatUsage(tokenCount=606, outputCount=134, inputCount=472)
16:12:42.805 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"抖音又是一家什么公司呢？","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:13:08.460 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"国内的BAT现在指代什么公司呢？","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:13:35.717 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"现在有新的指代了吗？你的回答有点过时了","username":"zhangsan","userId":"chengcheng.qin","conversationId":"7469699646986862626","responseSize":"12","toolContext":{"in":"test"},"toolCallOut":[]},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2024-12-18T16:39:34","version":"1.0"}
16:13:55.360 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
16:13:55.368 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
16:13:55.368 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730373 closed
16:13:55.395 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
16:13:55.399 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
16:13:55.400 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@3cbf453a was destroying!
16:13:55.400 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
