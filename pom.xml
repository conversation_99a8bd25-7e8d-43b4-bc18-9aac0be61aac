<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.6</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.winit</groupId>
    <packaging>pom</packaging>
    <artifactId>cobra-agent</artifactId>
    <version>${cobra-agent.version}</version>
    <name>cobra-agent</name>
    <description>Winit AI Agent</description>
    <properties>
        <spring-boot.version>2.4.6</spring-boot.version>
        <tomcat.version>9.0.98</tomcat.version>
        <cobra-agent.version>1.0.14-SNAPSHOT</cobra-agent.version>
        <maven_flatten_version>1.5.0</maven_flatten_version>
        <spring-ai.version>1.0.0-M3</spring-ai.version>
    </properties>
    <modules>
        <module>cobra-agent-core</module>
        <module>cobra-agent-spi</module>
        <module>cobra-agent-coze</module>
        <module>cobra-agent-common</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Repository</name>
            <url>http://***********:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Repository</name>
            <url>http://***********:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${maven_flatten_version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>oss</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>