package com.winit.cobra.agent.manager.impl;

import cn.hutool.core.builder.GenericBuilder;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.coze.openapi.client.chat.CancelChatReq;
import com.winit.cobra.agent.advisor.ChatManagerAdvisor;
import com.winit.cobra.agent.cache.CacheService;
import com.winit.cobra.agent.constant.ApiErrorCode;
import com.winit.cobra.agent.constant.RedisKeyConstant;
import com.winit.cobra.agent.coze.api.CozeAiApi;
import com.winit.cobra.agent.coze.api.CozeChatOption;
import com.winit.cobra.agent.coze.config.CozeChatOptionConfig;
import com.winit.cobra.agent.coze.model.CozeChatModel;
import com.winit.cobra.agent.enums.HumanAgentTpeEnum;
import com.winit.cobra.agent.enums.MessageTypeEnum;
import com.winit.cobra.agent.exception.ApiException;
import com.winit.cobra.agent.exception.ModelException;
import com.winit.cobra.agent.manager.ChatManager;
import com.winit.cobra.agent.repository.mybatis.entity.ChatMessageEntity;
import com.winit.cobra.agent.repository.mybatis.entity.ConversationEntity;
import com.winit.cobra.agent.repository.mybatis.mapper.ChatMessageMapper;
import com.winit.cobra.agent.repository.mybatis.mapper.ConversationMapper;
import com.winit.cobra.agent.spi.dto.request.*;
import com.winit.cobra.agent.spi.dto.response.*;
import com.winit.cobra.agent.util.Base64Util;
import com.winit.common.spi.context.CommandContext;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.ToolResponseMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.util.context.ContextView;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.winit.cobra.agent.constant.ApiErrorCode.CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_TAB;
import static com.winit.cobra.agent.constant.ApiErrorCode.TOO_FREQUENT_CALL;
import static com.winit.cobra.agent.constant.ChatContextBindConstant.CHAT_CONTEXT_BIND_DATA;
import static com.winit.cobra.agent.constant.ChatModelCommonParam.*;
import static com.winit.cobra.agent.constant.RedisKeyConstant.CHAT_ANSWERING;
import static com.winit.cobra.agent.constant.RedisKeyConstant.CONVERSATION_CHAT_ANSWERING;
import static com.winit.cobra.agent.exception.ModelExceptionCode.MODEL_CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_USER;

/**
 * <AUTHOR>
 * 历史会话管理
 */
@Component
public class ChatManagerImpl implements ChatManager {

    private static final Logger log = LoggerFactory.getLogger(ChatManagerImpl.class);
    @Resource
    private ConversationMapper conversationMapper;

    @Resource
    private ChatMessageMapper chatMessageMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private CozeChatOptionConfig cozeChatOptionConfig;

    @Resource
    private List<FunctionCallback> functionCallbacks;

    @Resource
    private CozeAiApi cozeAiApi;

    @Resource
    private ChatManagerAdvisor chatManagerAdvisor;

    @Override
    public void saveConversationChat(String userId, String conversationId, String chatId, List<ChatMessageEntity> content) {
        List<ChatMessageEntity> entities = content.stream().map(it -> {
            ChatMessageEntity entity = new ChatMessageEntity();
            entity.setModelConversationId(conversationId);
            entity.setModelChatId(chatId);
            entity.setRole(it.getRole());
            entity.setContent(it.getContent());
            return entity;
        }).collect(Collectors.toList());
        chatMessageMapper.insert(entities);

    }


    @Override
    public PageChatMessagesVo pageTopNConversationAndChat(String userId,
                                                          String conversationId,
                                                          LocalDateTime startTime,
                                                          LocalDateTime endTime,
                                                          boolean createdTimeDesc,
                                                          int pageNo,
                                                          int topN) {

        //userId => conversationId
        if (StringUtils.isEmpty(conversationId)) {
            PageDTO<ConversationEntity> conversationHistoryPageDTO = new PageDTO<>(0, 1);
            ConversationEntity conversationEntity = new ConversationEntity();
            LambdaQueryWrapper<ConversationEntity> conversationHisQueryWrapper = new LambdaQueryWrapper<>(conversationEntity);
            conversationHisQueryWrapper.orderByDesc(ConversationEntity::getCreated);
            conversationHisQueryWrapper.eq(ConversationEntity::getModeName, cozeChatOptionConfig.getModelName());
            List<ConversationEntity> conversationHistoryEntities = conversationMapper.selectList(conversationHistoryPageDTO, conversationHisQueryWrapper);
            if (conversationHistoryEntities.isEmpty()) {
                return new PageChatMessagesVo();
            } else {
                conversationId = conversationHistoryEntities.get(0).getModelConversationId();
            }
        }

        //conversationId => chatList
        ChatMessageEntity conversation = new ChatMessageEntity();
        conversation.setModelConversationId(conversationId);
        LambdaQueryWrapper<ChatMessageEntity> queryWrapper = new LambdaQueryWrapper<>(conversation);
        if (startTime != null) {
            queryWrapper.ge(ChatMessageEntity::getCreated, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(ChatMessageEntity::getCreated, endTime);
        }
        //默认排序 对话按时间轴来
        queryWrapper.orderByAsc(ChatMessageEntity::getCreated);
        //出现异常时候，会存在chatId为空的情况
        queryWrapper.isNotNull(ChatMessageEntity::getModelChatId);
        queryWrapper.ne(ChatMessageEntity::getModelChatId, "");
        // 调用分页查询方法
        Page<ChatMessageVo> page = new Page<>();
        page.setCurrent(pageNo);
        page.setSize(topN);
        Page<ChatMessageVo> chatMessagesWithFeedback = chatMessageMapper.getChatMessagesWithFeedback(page,
                conversationId, startTime, endTime
        );
        PageChatMessagesVo pageChatMessagesVo = new PageChatMessagesVo();
        pageChatMessagesVo.setChatMessages(chatMessagesWithFeedback.getRecords());
        pageChatMessagesVo.setTotal(chatMessagesWithFeedback.getTotal());
        //排序逻辑
        if (createdTimeDesc) {
            List<ChatMessageVo> collect = chatMessagesWithFeedback.getRecords().stream()
                    .sorted(Comparator.comparing(ChatMessageVo::getTimestamp).reversed())
                    .peek(chatMessage -> chatMessage.setDebugContent(Base64Util.decodeBase64(chatMessage.getDebugContent())))
                    .collect(Collectors.toList());
            pageChatMessagesVo.setChatMessages(collect);
        } else {
            List<ChatMessageVo> collect = chatMessagesWithFeedback.getRecords().stream()
                    .sorted(Comparator.comparing(ChatMessageVo::getTimestamp))
                    .peek(chatMessage -> chatMessage.setDebugContent(Base64Util.decodeBase64(chatMessage.getDebugContent())))
                    .collect(Collectors.toList());
            pageChatMessagesVo.setChatMessages(collect);
        }
        return pageChatMessagesVo;
    }

    @Override
    public List<ListChatMessageVo> listChatMessages(MessageTypeEnum messageType, LocalDateTime startTime) {
        LambdaQueryWrapper<ChatMessageEntity> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.ge(ChatMessageEntity::getCreated, startTime);
        queryWrapper.eq(ChatMessageEntity::getRole, messageType.getValue().toLowerCase());
        queryWrapper.orderByAsc(ChatMessageEntity::getCreated);

        List<ChatMessageEntity> entities = chatMessageMapper.selectList(queryWrapper);
        return entities.stream().map(it -> {
            ListChatMessageVo chatMessagesVo = new ListChatMessageVo();
            chatMessagesVo.setModelChatId(it.getModelChatId());
            chatMessagesVo.setContent(it.getContent());
            chatMessagesVo.setModelChatId(it.getModelChatId());
            chatMessagesVo.setModelConversationId(it.getModelConversationId());
            chatMessagesVo.setDebugContent(it.getDebugContent());
            chatMessagesVo.setRole(it.getRole());
            chatMessagesVo.setCreated(it.getCreated());
            return chatMessagesVo;
        }).toList();
    }

    @Override
    @Transactional
    public void updateChatDebugInfo(String userId, String conversationId, String chatId, String debugInfo) {

        LambdaQueryWrapper<ChatMessageEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessageEntity::getModelChatId, chatId);
        queryWrapper.eq(ChatMessageEntity::getModelConversationId, conversationId);
        queryWrapper.eq(ChatMessageEntity::getRole, MessageTypeEnum.ASSISTANT.getValue().toLowerCase());
        ChatMessageEntity updateEntity = new ChatMessageEntity();
        updateEntity.setDebugContent(debugInfo);
        updateEntity.setUpdatedby("System_debug");
        chatMessageMapper.update(updateEntity, queryWrapper);

        cacheService.remove(buildDebugKeyId(userId, conversationId));
    }

    @Override
    public void cachedChatDebugInfo(String userId, String conversationId, String chatId, String debugInfo) {
        //写入userId + conversationId => debugInfo
        String keyId = buildDebugKeyId(userId, conversationId);
        cacheService.put(keyId, debugInfo, 50, TimeUnit.SECONDS);
    }

    private String buildDebugKeyId(String userId, String conversationId) {
        return RedisKeyConstant.DEBUG_INFO_ID
                .of(userId, conversationId);
    }

    @Override
    public QueryChatDebugInfoVo queryChatDebugInfo(String userId, String conversationId, String chatId) {
        LambdaQueryWrapper<ChatMessageEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessageEntity::getModelChatId, chatId);
        queryWrapper.eq(ChatMessageEntity::getModelConversationId, conversationId);
        queryWrapper.eq(ChatMessageEntity::getRole, MessageTypeEnum.ASSISTANT.getValue().toLowerCase());
        ChatMessageEntity chatMessageEntity = chatMessageMapper.selectOne(queryWrapper);
        if (chatMessageEntity == null) {
            return new QueryChatDebugInfoVo();
        }
        String debugContent = chatMessageEntity.getDebugContent();
        QueryChatDebugInfoVo queryChatDebugInfoVo = new QueryChatDebugInfoVo();
        queryChatDebugInfoVo.setDebugInfo(Base64Util.decodeBase64(debugContent));
        queryChatDebugInfoVo.setChatId(chatId);
        queryChatDebugInfoVo.setConversationId(conversationId);
        return queryChatDebugInfoVo;
    }

    @Override
    public TransferToHumanVo transferToHuman(TransferToHumanCommand command) {

        //
        if (command.getToHumanContent() != null &&
                HumanAgentTpeEnum.U_DESK.getValue().equalsIgnoreCase(command.getToHumanContent().getHumanAgentType())) {

            //用户必须存在会话先
            LambdaQueryWrapper<ConversationEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ConversationEntity::getModelConversationId, command.getConversationId());
            queryWrapper.eq(ConversationEntity::getUserId, command.getUserId());
            boolean exists = conversationMapper.exists(queryWrapper);
            if (!exists) {
                throw new ApiException(ApiErrorCode.USER_NOT_EXIST_RELATIVE_CONVERSATION);
            }

            TransferToHumanVo transferToHumanVo = new TransferToHumanVo();
            //写入一条数据到对话列表
            ChatMessageEntity chatMessageEntity = new ChatMessageEntity();
            chatMessageEntity.setContent(buildTransferHumanSpecialContent(command));
            chatMessageEntity.setCreatedby("client_transfer_to_human");
            chatMessageEntity.setModelConversationId(command.getConversationId());
            chatMessageEntity.setModelChatId(command.getChatId());
            chatMessageEntity.setRole(MessageTypeEnum.HUMAN_AGENT.getValue().toLowerCase());
            chatMessageMapper.insert(chatMessageEntity);
            return transferToHumanVo;
        } else {
            throw new RuntimeException("Human agent type is not supported");
        }

    }

    /**
     * buildTransferHumanSpecialContent
     *
     * @param command
     * @return
     */
    private String buildTransferHumanSpecialContent(TransferToHumanCommand command) {
        return JSON.toJSONString(command.getToHumanContent());
    }

    @Deprecated
    @Override
    public void bindHumanAgentSession(ListChatMessageVo chatMessageVo, Long subSessionId) {
        if (HumanAgentTpeEnum.U_DESK.getValue().equalsIgnoreCase(chatMessageVo.getRole())) {
            LambdaQueryWrapper<ChatMessageEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatMessageEntity::getId, chatMessageVo.getId());
            queryWrapper.eq(ChatMessageEntity::getRole, MessageTypeEnum.HUMAN_AGENT.getValue().toLowerCase());

            //写入一条数据到对话列表
            ChatMessageEntity chatMessageEntity = new ChatMessageEntity();
            chatMessageEntity.setContent(buildUpdatedContent(subSessionId, chatMessageVo.getContent()));
            chatMessageEntity.setUpdatedby("uschedule_client_transfer_to_human");
            chatMessageMapper.update(chatMessageEntity, queryWrapper);
        } else {
            throw new RuntimeException("Human agent type is not supported");
        }
    }

    @Override
    public List<ChatMessageVo> findTransferHumanChat(ListTransformHumanChatCommand command) {
        LambdaQueryWrapper<ChatMessageEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ChatMessageEntity::getContent, command.getTransferIdentity());
        ChatMessageEntity chatMessageEntity = chatMessageMapper.selectOne(queryWrapper);
        return Lists.newArrayList(chatMessageEntity).stream()
                .map(chatMessage -> GenericBuilder.of(ChatMessageVo::new)
                        .with(ChatMessageVo::setContent, chatMessage.getContent())
                        .with(ChatMessageVo::setRole, chatMessage.getRole())
                        .with(ChatMessageVo::setTimestamp, chatMessage.getUpdated().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                        .with(ChatMessageVo::setChatId, chatMessage.getModelChatId())
                        .with(ChatMessageVo::setConversationId, chatMessage.getModelConversationId())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public void cancelChat(CancelChatCommand command) {
        try {
            CancelChatReq cancelChatReq = CancelChatReq.builder()
                    .chatID(command.getChatId())
                    .conversationID(command.getConversationId())
                    .build();
            cozeAiApi.cancelChat(cancelChatReq);
        } catch (Exception e) {
            //java.lang.RuntimeException:
            // java.lang.RuntimeException:
            // CozeApiException(code=4001, msg=Invalid chat (including wrong chat id, chat cannot be found), logID=202506161014279D708228AD88D5653651)
            log.warn("Chat has completed skip cancel{}", e.getMessage());
        }finally {
            String chatAnsweringKey = CONVERSATION_CHAT_ANSWERING.of(command.getUserId(),command.getConversationId());
            cacheService.remove(chatAnsweringKey);
        }


    }


    /**
     * 流对话
     *
     * @param chatCommand
     * @param ctx
     * @return
     */
    public @NotNull Flux<ChatVo> fireChatStreamBiz(StreamChatCommand chatCommand, ContextView ctx) {

        String locale = ctx.getOrDefault(CommandContext.LOCALE, Locale.US.getDisplayLanguage());
        CommandContext.getContext().setLanguage(locale);
        String chatAnsweringKey = CHAT_ANSWERING.of(chatCommand.getUserId());
        String conversationChatAnswering = CONVERSATION_CHAT_ANSWERING.of(chatCommand.getUserId(), chatCommand.getConversationId());
        //如果是绑定数据的请求，则不进行限流 避免出现异常
        if (!CHAT_CONTEXT_BIND_DATA.equals(chatCommand.getChatId())) {
            String chatAnsweringKeyValue = cacheService.get(chatAnsweringKey);
            if (chatAnsweringKeyValue != null) {
                throw new ApiException(TOO_FREQUENT_CALL);
            } else {
                cacheService.put(chatAnsweringKey, chatCommand.getConversationId(), 1_000L, TimeUnit.MILLISECONDS);
            }

            String conversationChatAnsweringValue = cacheService.get(conversationChatAnswering);
            if (conversationChatAnsweringValue != null) {
                throw new ApiException(CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_TAB);
            } else {
                cacheService.put(conversationChatAnswering, conversationChatAnswering, cozeChatOptionConfig.getReadTimeout(), TimeUnit.MILLISECONDS);
            }
        }

        try {
            String conversationId = fireConversationId(chatCommand);
            ChatClient.ChatClientRequestSpec chatClient = createChatClient(chatCommand, conversationId);
            Flux<ChatResponse> responseFlux = chatClient.stream().chatResponse();
            return responseFlux.concatMap(Flux::just)
                    .map(it -> buildChatVo(conversationId, it))
                    .onErrorResume(it -> {
                        CommandContext.getContext().setLanguage(locale);
                        if (it instanceof IllegalStateException) {
                            throw new ApiException(CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_TAB);
                        } else if (it instanceof ModelException && ((ModelException) it).getErrorCode()
                                .equals(MODEL_CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_USER)) {
                            throw new ApiException(CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_TAB);
                        } else {
                            throw new ApiException(ApiErrorCode.SERVICE_ACCESS_EXCEPTION, it.getMessage());
                        }
                    }).doFinally(it -> {
                        cleanContext(chatAnsweringKey, conversationChatAnswering);
                        updateConversationUpdatedTime(chatCommand, conversationId);
                    });
        } catch (Exception e) {
            log.warn("Chat error: {}", e.getMessage());
            cleanContext(chatCommand.getUserId(), chatCommand.getConversationId());
            throw new ApiException(ApiErrorCode.SERVICE_ACCESS_EXCEPTION, e.getMessage());
        }

    }


    @Deprecated
    private String buildUpdatedContent(Long subSessionId, String content) {
        UDeskHumanAgentIndentityVo uDeskHumanAgentIndentityVo = JSON.parseObject(content, UDeskHumanAgentIndentityVo.class);
        uDeskHumanAgentIndentityVo.setSubSessionId(String.valueOf(subSessionId));
        return JSON.toJSONString(uDeskHumanAgentIndentityVo);
    }


    /**
     * 清理上下文
     *
     * @param chatAnsweringKey
     * @param conversationChatAnswering
     */
    private void cleanContext(String chatAnsweringKey, String conversationChatAnswering) {
        CommandContext.getContext().clean();
        cacheService.remove(chatAnsweringKey);
        cacheService.remove(conversationChatAnswering);
    }


    private String fireConversationId(BaseChatCommand command) {
        String conversationId = command.getConversationId();
        String userId = command.getUserId();
        if (StringUtils.isEmpty(conversationId)) {
            log.error("conversationId is null,a new conversation start, create conversation for user:{}", userId);
            throw new RuntimeException("conversationId is null, start conversation for user first:" + userId);
        }

        return conversationId;
    }

    /**
     * 创建对话客户端
     *
     * @param chatCommand
     * @param conversationId
     * @return
     */
    private ChatClient.ChatClientRequestSpec createChatClient(BaseChatCommand chatCommand, String conversationId) {
        return ChatClient.create(buildCozeChatModel(chatCommand))
                .prompt()
                .advisors(advisorSpec -> advisorSpec
                        .param(USER_ID, chatCommand.getUserId())
                        .param(USERNAME, chatCommand.getUsername())
                        .param(CONVERSATION_ID, chatCommand.getConversationId())
                        .param(CHAT_ID, chatCommand.getChatId() == null ? "" : chatCommand.getChatId())
                        .param(AUTO_SAVE_HISTORY, chatCommand.isAutoSaveHistory())
                        .advisors(chatManagerAdvisor)
                )
                .user(buildUserMessage(chatCommand))
                .messages(buildAdditionalMessage(chatCommand.getToolCallOut()));
    }


    private CozeChatModel buildCozeChatModel(BaseChatCommand chatRequest) {
        return new CozeChatModel(buildCozeChatOption(chatRequest), cozeAiApi);
    }


    private List<Message> buildAdditionalMessage(List<SubmitToolCallCommand> toolCallOut) {
        if (toolCallOut != null && !toolCallOut.isEmpty()) {
            List<Message> messages = new ArrayList<>();
            ToolResponseMessage toolResponseMessage = new ToolResponseMessage(
                    toolCallOut.stream().map(submitToolCallCommand -> new ToolResponseMessage.ToolResponse(
                            submitToolCallCommand.getToolCallId(),
                            submitToolCallCommand.getFunctionName(),
                            submitToolCallCommand.getOutput()
                    )).collect(Collectors.toList()));
            messages.add(toolResponseMessage);
            return messages;

        }

        return Lists.emptyList();
    }

    private String buildUserMessage(BaseChatCommand chatRequest) {
        if (chatRequest.getToolCallOut() != null && !chatRequest.getToolCallOut().isEmpty()) {
            return "TOOLS_CALL";
        }
        return chatRequest.getMessage();
    }

    private CozeChatOption buildCozeChatOption(BaseChatCommand baseCommand) {

        return CozeChatOption.builder()
                .withToolContext(baseCommand.getToolContext())
                .withBotId(cozeChatOptionConfig.getBotID())
                .withFunctionCallbacks(loadAllFunctionCallbacks())
                .withConversationId(baseCommand.getConversationId())
                .withChatId(baseCommand.getChatId())
                .withUserId(baseCommand.getUserId())
                .withMetadata(baseCommand.getMetaData())
                .withUsername(baseCommand.getUsername())
                .withCustomVariables(baseCommand.getCustomVariables())
                .withAutoSaveHistory(baseCommand.isAutoSaveHistory())
                .withProxyToolCalls(true)
                .build();
    }


    /**
     * 获取所有实现了接口{@link FunctionCallback}的实现类
     *
     * @return
     */
    private List<FunctionCallback> loadAllFunctionCallbacks() {
        return functionCallbacks;
    }


    private ChatVo buildChatVo(String conversationId, ChatResponse chatResponse) {
        if (chatResponse == null) {
            throw new ApiException(CURRENT_CONVERSATION_PROCESSING_IN_ANOTHER_TAB);
        }
        // 创建 ChatVo 对象
        ChatVo chatVo = new ChatVo();
        chatVo.setConversationId(conversationId);
        chatVo.setContent(buildContent(chatResponse));
        chatVo.setChatId(chatResponse.getMetadata().getId());
        chatVo.setLogId(buildLogId(chatResponse));
        return chatVo;
    }

    private String buildLogId(ChatResponse chatResponse) {
        if (chatResponse == null) {
            return null;
        }

        if (chatResponse.getResult() == null) {
            return null;
        }

        if (chatResponse.getResult().getOutput() == null) {
            return null;
        }

        Object object = chatResponse.getMetadata().get(LOG_ID);
        if (object != null) {
            return object.toString();
        }

        return null;
    }

    private String buildContent(ChatResponse chatResponse) {
        StringBuffer content = new StringBuffer();
        chatResponse.getResults().stream().filter(it -> !it.getOutput().getContent().isEmpty())
                .forEach(it -> content.append(it.getOutput().getContent()));

        return content.toString();
    }

    /**
     * 更新会话的最后更新时间
     *
     * @param chatCommand
     * @param conversationId
     */
    private void updateConversationUpdatedTime(StreamChatCommand chatCommand, String conversationId) {
        //需要更新会话时间
        LambdaQueryWrapper<ConversationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConversationEntity::getModelConversationId, conversationId);
        queryWrapper.eq(ConversationEntity::getCustomerCode, chatCommand.getCustomerCode());
        queryWrapper.eq(ConversationEntity::getUserId, chatCommand.getUserId());
        ConversationEntity conversationEntity = new ConversationEntity();
        conversationEntity.setUpdated(LocalDateTime.now());
        conversationEntity.setUpdatedby("ChatCompletedUpdated");
        conversationMapper.update(conversationEntity, queryWrapper);
    }

}

