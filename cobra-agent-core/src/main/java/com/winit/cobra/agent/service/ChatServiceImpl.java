package com.winit.cobra.agent.service;

import com.alibaba.fastjson.JSON;
import com.winit.cobra.agent.coze.config.CozeChatOptionConfig;
import com.winit.cobra.agent.exception.ApiException;
import com.winit.cobra.agent.lock.RedisDistributedLockTemplate;
import com.winit.cobra.agent.manager.ChatManager;
import com.winit.cobra.agent.spi.dto.request.*;
import com.winit.cobra.agent.spi.dto.response.*;
import com.winit.cobra.agent.spi.service.ChatService;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 聊天服务
 */
@Service("chatService")
@DubboService(interfaceClass = ChatService.class, version = "${dubbo.provider.version}")
public class ChatServiceImpl implements ChatService {

    private static final Logger log = LoggerFactory.getLogger(ChatServiceImpl.class);

    @Resource
    private ChatManager chatManager;


    @Resource
    private CozeChatOptionConfig cozeChatOptionConfig;


    @Resource
    private RedisDistributedLockTemplate redisDistributedLockTemplate;

    @Override
    public ChatVo chat(ChatCommand command) {

        throw new ApiException("NOT SUPPORTED");

    }


    @Override
    public Flux<ChatVo> streamChat(StreamChatCommand chatCommand) {
        return Flux.deferContextual(ctx ->
                chatManager.fireChatStreamBiz(chatCommand, ctx));
    }


    @Override
    public TransferToHumanVo transferToHuman(TransferToHumanCommand command) {
        return chatManager.transferToHuman(command);
    }


    @Override
    public PageChatMessagesVo listChat(ListChatCommand command) {
        return chatManager.pageTopNConversationAndChat(
                command.getUserId(),
                command.getConversationId(),
                command.getStartTime() == null ? null : LocalDateTime.parse(command.getStartTime()),
                command.getEndTime() == null ? null : LocalDateTime.parse(command.getEndTime()),
                command.isCreatedTimeDesc(),
                command.getPageNo(),
                command.getTopN());
    }

    @Override
    public PageChatMessagesVo listTransformHumanChat(ListTransformHumanChatCommand command) {
        //
        List<ChatMessageVo> chatMessageVos = chatManager.findTransferHumanChat(command);
        if (chatMessageVos.isEmpty()) {
            return new PageChatMessagesVo();
        } else {
            PageChatMessagesVo pageChatMessagesVo = new PageChatMessagesVo();
            pageChatMessagesVo.setChatMessages(chatMessageVos);
            pageChatMessagesVo.setTotal(chatMessageVos.size());
            return pageChatMessagesVo;
        }

    }

    @Override
    public void cancelChat(CancelChatCommand command) {
        chatManager.cancelChat(command);
    }


}
