package com.winit.cobra.agent.constant;

/**
 * Redis key
 * <AUTHOR>
 */
public class RedisKeyConstant {


    /**
     * debug key id
     */
    public static final RedisKeyConstant DEBUG_INFO_ID = new RedisKeyConstant("DEBUG_INFO:userId%s:conversationId:%s");


    /**
     * 用户已经发起对话进行中，限流
     */
    public static final RedisKeyConstant CHAT_ANSWERING = new RedisKeyConstant("CHAT_ANSWERING:userId:%s");


    /**
     *同一会话对话进行中
     */
    public static final RedisKeyConstant CONVERSATION_CHAT_ANSWERING = new RedisKeyConstant("CHAT_ANSWERING_CONVERSATION:userId:%s:conversationId:%s");

    /**
     * 接口异常告警
     */
    public static final RedisKeyConstant ALERT_API_ERROR_KEY = new RedisKeyConstant("ALERT_API_ERROR:api:%s");

    private final StringBuilder keyBuilder;

    private RedisKeyConstant(String prefix) {
        this.keyBuilder = new StringBuilder(prefix);
    }

    public String of(String... value) {
        return String.format(this.keyBuilder.toString(), (Object[]) value);
    }
}
