020102503390=Success
#\u03F5\u0373\uFFFD\uCCE3
01010250001=System error
01010250002=The service is currently busy, please try again later
01010250004=The system is busy, please try again later
01010250005=System maintenance, please try again later
01010250006=Access Error: Interface version does not exist, please check
#\uFFFD\uFFFD\u0228\uFFFD\uFFFD\uFFFD\uFFFD
03010250001=Input app_ Key: [ the parameter_ Key] not found, app_ Key is login seller.winit.com 's mailbox
***********=Illegal json string,please check.
***********=Data encoding is not supported
***********=Access is too frequent,please retry later
***********=Signing failed. Please check the Token is correct, the recipient information contains special characters, and use UTF-8 encoding.
***********=User does not exist
***********=Simultaneous login by multiple users can lead to conversation records appearing from consultations not initiated by the current user. It is advised to create and use sub-accounts to prevent interference.
***********=Conversation does not exist, translate to human agent failed
***********=You've initiated conversations too frequently. Please wait a while and try again later.
***********=ClientId:{0} already exist
***********=ClientId:{0} not exist
***********=Coze workflow run error: {0}
