package com.winit.cobra.agent.spi.dto.request;

import com.winit.common.spi.command.SPICommand;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.NonNull;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel(description = "")
public class RunWorkflowCommand extends BaseCommand implements Serializable {

    /**
     * 扣子的客户端id用于查找对应的授权信息
     */
    @ApiModelProperty("扣子的客户端id用于查找对应的授权信息")
    private String clientId;
    /*
     * 工作流ID，该工作流应已发布
     */
    @ApiModelProperty("工作流ID，该工作流应已发布")
    @NonNull
    private String workflowId;

    /*
     * 工作流起始节点的输入参数及其值。可在指定工作流的编排页面查看参数列表
     */
    @ApiModelProperty("工作流起始节点的输入参数及其值。可在指定工作流的编排页面查看参数列表")
    private Map<String, Object> parameters;

    /*
     * 某些工作流执行所需的关联Bot ID，
     * 例如包含数据库节点、变量节点等工作流
     */
    @ApiModelProperty(value = "某些工作流执行所需的关联Bot ID， 例如包含数据库节点、变量节点等工作流",hidden = true)
    private String botId;

    /*
     * 用于指定一些额外字段，格式为Map[String][String]
     */
    @ApiModelProperty(value = "用于指定一些额外字段，格式为Map[String][String]",hidden = true)
    private Map<String, String> ext;

    /*
     * 是否异步运行
     */
    @ApiModelProperty(value = "是否异步运行",hidden = true)
    @JsonProperty("isAsync")
    private Boolean isAsync = false;

    /*
     * 工作流所在的应用ID
     */
    @ApiModelProperty(value = "工作流所在的应用ID",hidden = true)
    private String appId;

    public @NonNull String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(@NonNull String workflowId) {
        this.workflowId = workflowId;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public String getBotId() {
        return botId;
    }

    public void setBotId(String botId) {
        this.botId = botId;
    }

    public Map<String, String> getExt() {
        return ext;
    }

    public void setExt(Map<String, String> ext) {
        this.ext = ext;
    }

    public Boolean getIsAsync() {
        return isAsync;
    }

    public void setIsAsync(Boolean async) {
        isAsync = async;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }
}