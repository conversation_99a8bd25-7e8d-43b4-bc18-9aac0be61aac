13:01:40.975 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m22s969ms).
13:01:44.649 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 1032963ms for sessionid 0x19439960e730560, closing socket connection and attempting reconnect
13:01:44.760 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
13:01:46.564 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:01:46.569 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:01:46.575 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
13:01:46.575 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730560 has expired, closing socket connection
13:01:46.575 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@1dc2c13c
13:01:46.577 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
13:01:46.577 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:01:46.577 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
13:01:46.582 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:01:46.588 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730561, negotiated timeout = 60000
13:01:46.589 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
13:05:00.001 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 165312ms for sessionid 0x19439960e730561, closing socket connection and attempting reconnect
13:05:00.118 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
13:05:01.306 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:05:01.312 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:05:01.324 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
13:05:01.324 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@1dc2c13c
13:05:01.324 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730561 has expired, closing socket connection
13:05:01.328 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
13:05:01.328 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
13:05:01.328 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:05:01.334 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:05:01.340 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730562, negotiated timeout = 60000
13:05:01.340 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
13:05:06.345 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m55s328ms).
13:08:17.462 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 167471ms for sessionid 0x19439960e730562, closing socket connection and attempting reconnect
13:08:17.570 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
13:08:18.987 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:08:19.008 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:08:19.012 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
13:08:19.012 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730562 has expired, closing socket connection
13:08:19.013 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@1dc2c13c
13:08:19.014 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:08:19.014 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
13:08:19.015 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
13:08:19.018 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:08:19.025 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730563, negotiated timeout = 60000
13:08:19.026 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
13:08:33.843 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m57s487ms).
13:09:29.634 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=55s790ms).
13:09:33.241 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 45774ms for sessionid 0x19439960e730563, closing socket connection and attempting reconnect
13:09:33.352 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
13:09:34.698 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:09:34.703 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:09:34.709 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730563, negotiated timeout = 60000
13:09:34.709 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
13:11:38.968 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 46184ms for sessionid 0x19439960e730563, closing socket connection and attempting reconnect
13:11:39.070 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
13:11:41.034 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
13:11:41.044 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
13:11:41.049 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730563, negotiated timeout = 60000
13:11:41.049 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
13:12:05.401 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=56s200ms).
