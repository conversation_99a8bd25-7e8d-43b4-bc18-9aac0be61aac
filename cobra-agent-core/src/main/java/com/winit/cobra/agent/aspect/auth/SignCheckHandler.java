package com.winit.cobra.agent.aspect.auth;

import cn.hutool.crypto.SecureUtil;
import com.winit.cobra.agent.constant.ApiErrorCode;
import com.winit.cobra.agent.exception.ApiException;
import com.winit.cobra.agent.repository.ums.UmsUserServiceRepo;
import com.winit.cobra.agent.repository.ums.vo.UserVo;
import com.winit.cobra.agent.spi.dto.request.RequestCommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.winit.cobra.agent.util.SignUtil.buildJsonStrByRequestMsg;

/**
 * <AUTHOR>
 * 签名操作
 * 签名验证
 */
@Component("signCheckHandler")
public class SignCheckHandler implements InterceptorHandler<RequestCommand<?>> {

    private static final Logger logger = LoggerFactory.getLogger(SignCheckHandler.class);

    @Resource
    private UmsUserServiceRepo umsUserServiceRepo;

    @Override
    public void handle(RequestCommand<?> requestCommand) {
//
//        //验证用是否存在
//        checkUserExist(requestCommand.getApp_key());
//        //原文
//        String content = buildJsonStrByRequestMsg(requestCommand, signKey());
//        //构造签名
//        String signedContent = buildSign(content);
//        //验证签名
//        checkMd5Sign(signedContent, requestCommand.getSign());
//        //验证请求时间戳
//        checkRequestTimeStamp(requestCommand.getTimestamp());
    }


    /**
     * checkUserExist
     *
     * @param username
     */
    private void checkUserExist(String username) {
        try {
            UserVo userVo = umsUserServiceRepo.queryByUsername(username);
            if (null == userVo) {
                throw new ApiException(ApiErrorCode.USER_NOT_EXIST_ERROR);
            }
        } catch (Exception e) {
            throw new ApiException(ApiErrorCode.USER_NOT_EXIST_ERROR, e.getMessage());
        }

    }

    /**
     * checkRequestTimeStamp
     *
     * @param timestamp
     */
    private void checkRequestTimeStamp(String timestamp) {
        long currentTimeMillis = System.currentTimeMillis();
        long requestTimestamp = Long.parseLong(timestamp);
        //5分钟内
        if (currentTimeMillis - requestTimestamp > 5 * 60 * 1000L) {
            logger.info("请求超时");
            throw new ApiException(ApiErrorCode.API_AUTH_TOKEN_FAILED);
        }
    }

    /**
     * checkMd5Sign
     *
     * @param signedContent
     * @param sign
     */
    private void checkMd5Sign(String signedContent, String sign) {
        boolean viableSign = signedContent.equals(sign);
        if (!viableSign) {
            logger.info("请求鉴权结果失败");
            throw new ApiException(ApiErrorCode.API_AUTH_TOKEN_FAILED);
        }
    }

    /**
     * signKey
     * @return
     */
    private String signKey() {
        return "winit-XA-Cobra@2025#Ex";
    }

    /**
     * buildSign
     * @param content
     * @return
     */
    private String buildSign(String content) {
        // 将密钥key拼接在字典排序后的参数字符串中,得到待签名字符串。
        // 使用md5算法加密待加密字符串并转为大写即为sign
        return SecureUtil.md5(content).toUpperCase();
    }
}
