19:24:46.299 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:24:46.301 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:24:46.311 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:24:46.332 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 22564 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:24:46.333 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:24:46.701 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:24:46.702 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:24:46.702 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:24:46.702 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:24:46.702 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:24:46.839 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:24:46.839 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:24:46.839 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:24:46.839 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:24:46.840 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:24:46.840 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:24:46.842 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:24:46.870 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:24:46.886 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:24:47.287 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:24:47.291 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:24:47.292 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:24:47.292 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:24:47.336 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:24:47.336 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 978 ms
19:24:47.824 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:24:47.831 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:24:47.838 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:24:47.841 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:24:47.846 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:24:47.855 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:24:47.859 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:24:47.892 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:24:47.903 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:24:47.908 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@2fd05b15
19:24:47.916 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:24:47.916 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:24:47.922 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:24:47.930 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730575, negotiated timeout = 60000
19:24:47.933 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:24:48.715 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:24:48.820 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
19:24:49.281 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:24:49.287 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:24:49.518 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.364 seconds (JVM running for 4.437)
19:24:49.963 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:24:49.963 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:24:49.964 [RMI TCP Connection(2)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:24:49.965 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
19:24:50.201 [RMI TCP Connection(2)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:24:56.775 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:24:56.898 [http-nio-9090-exec-1] [] INFO  c.w.c.a.aspect.auth.SignCheckHandler - 请求超时
19:24:56.905 [http-nio-9090-exec-1] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - 03010250005
com.winit.cobra.agent.exception.ApiException: 03010250005
	at com.winit.cobra.agent.aspect.auth.SignCheckHandler.checkRequestTimeStamp(SignCheckHandler.java:74)
	at com.winit.cobra.agent.aspect.auth.SignCheckHandler.handle(SignCheckHandler.java:42)
	at com.winit.cobra.agent.aspect.auth.SignCheckHandler.handle(SignCheckHandler.java:22)
	at com.winit.cobra.agent.aspect.auth.ApiRequestAuthAspect.verify(ApiRequestAuthAspect.java:61)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:617)
	at org.springframework.aop.aspectj.AspectJMethodBeforeAdvice.before(AspectJMethodBeforeAdvice.java:44)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.winit.cobra.agent.controller.ChatController$$EnhancerBySpringCGLIB$$f9647f0.chatWithStream(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1063)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:416)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:348)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:285)
	at org.springframework.web.servlet.view.InternalResourceView.renderMergedOutputModel(InternalResourceView.java:171)
	at org.springframework.web.servlet.view.AbstractView.render(AbstractView.java:316)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1396)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1080)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.winit.cobra.agent.filter.CachingRequestBodyFilter.doFilter(CachingRequestBodyFilter.java:27)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:25:05.918 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:25:05.925 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730575 closed
19:25:05.925 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:25:05.948 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:25:05.951 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:25:05.952 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@7cf96d03 was destroying!
19:25:05.952 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:25:09.053 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:25:09.054 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:25:09.065 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:25:09.084 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 22675 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:25:09.084 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:25:09.406 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:25:09.407 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:25:09.407 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:25:09.407 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:25:09.407 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:25:09.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:25:09.534 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:25:09.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:25:09.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:25:09.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:25:09.534 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:25:09.537 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:25:09.564 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:25:09.578 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:25:09.944 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:25:09.948 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:25:09.948 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:25:09.948 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:25:09.988 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:25:09.988 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 886 ms
19:25:10.530 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:25:10.537 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:25:10.544 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:25:10.547 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:25:10.552 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:25:10.560 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:25:10.564 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:25:10.594 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:25:10.605 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:25:10.608 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:25:10.608 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:25:10.608 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:25:10.608 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:25:10.608 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:25:10.608 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:25:10.609 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@34604b32
19:25:10.615 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:25:10.615 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:25:10.624 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:25:10.633 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730576, negotiated timeout = 60000
19:25:10.635 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:25:11.383 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:25:11.537 [main] [] INFO  org.reflections.Reflections - Reflections took 8 ms to scan 1 urls, producing 2 keys and 2 values 
19:25:11.940 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:25:11.945 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:25:12.092 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.192 seconds (JVM running for 3.663)
19:25:12.204 [RMI TCP Connection(2)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:25:12.205 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:25:12.205 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:25:12.207 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
19:25:12.324 [RMI TCP Connection(2)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:25:13.429 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:25:13.520 [http-nio-9090-exec-1] [] WARN  o.s.w.s.m.m.a.ReactiveTypeHandler - 
!!!
Streaming through a reactive type requires an Executor to write to the response.
Please, configure a TaskExecutor in the MVC config under "async support".
The SimpleAsyncTaskExecutor currently in use is not suitable under load.
-------------------------------
Controller:	com.winit.cobra.agent.controller.ChatController
Method:		chatWithStream
Returning:	reactor.core.publisher.Flux<com.winit.cobra.agent.spi.dto.response.ResponseVo<com.winit.cobra.agent.spi.dto.response.ChatVo>>
!!!
19:25:27.593 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:25:51.867 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:26:05.520 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:26:34.362 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:26:34.369 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730576 closed
19:26:34.369 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:26:34.385 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:26:34.389 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:26:34.390 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@70a01334 was destroying!
19:26:34.390 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:27:13.261 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:27:13.263 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:27:13.276 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:27:13.305 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 23179 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:27:13.306 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:27:13.732 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:27:13.733 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:27:13.733 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:27:13.733 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:27:13.733 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:27:13.860 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:27:13.860 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:27:13.860 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:27:13.860 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:27:13.860 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:27:13.860 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:27:13.863 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:27:13.888 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:27:13.903 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:27:14.268 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:27:14.272 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:27:14.273 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:27:14.273 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:27:14.314 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:27:14.314 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 978 ms
19:27:14.758 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:27:14.764 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:27:14.770 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:27:14.773 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:27:14.778 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:27:14.787 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:27:14.791 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:27:14.826 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:27:14.838 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:27:14.843 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@2fd05b15
19:27:14.850 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:27:14.851 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:27:14.860 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:27:14.867 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730577, negotiated timeout = 60000
19:27:14.869 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:27:15.564 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:27:15.664 [main] [] INFO  org.reflections.Reflections - Reflections took 5 ms to scan 1 urls, producing 2 keys and 2 values 
19:27:16.000 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:27:16.004 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:27:16.155 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.084 seconds (JVM running for 3.717)
19:27:16.268 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:27:16.268 [RMI TCP Connection(3)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:27:16.268 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:27:16.269 [RMI TCP Connection(1)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
19:27:16.399 [RMI TCP Connection(3)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:27:17.993 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:27:30.551 [http-nio-9090-exec-1] [] WARN  o.s.w.s.m.m.a.ReactiveTypeHandler - 
!!!
Streaming through a reactive type requires an Executor to write to the response.
Please, configure a TaskExecutor in the MVC config under "async support".
The SimpleAsyncTaskExecutor currently in use is not suitable under load.
-------------------------------
Controller:	com.winit.cobra.agent.controller.ChatController
Method:		chatWithStream
Returning:	reactor.core.publisher.Flux<com.winit.cobra.agent.spi.dto.response.ResponseVo<com.winit.cobra.agent.spi.dto.response.ChatVo>>
!!!
19:27:34.634 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:27:44.967 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:28:08.218 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:28:19.595 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:28:59.799 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:28:59.808 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730577 closed
19:28:59.808 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:28:59.827 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:28:59.831 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:28:59.832 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@f7508dc was destroying!
19:28:59.832 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:29:03.398 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:29:03.400 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:29:03.410 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:29:03.430 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 23594 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:29:03.431 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:29:03.881 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:29:03.881 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:29:03.881 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:29:03.882 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:29:03.882 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:29:04.100 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:29:04.100 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:29:04.100 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:29:04.100 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:29:04.100 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:29:04.100 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:29:04.105 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:29:04.143 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:29:04.163 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:29:05.451 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:29:05.457 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:29:05.459 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:29:05.459 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:29:05.523 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:29:05.524 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2070 ms
19:29:06.095 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:29:06.103 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:29:06.111 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:29:06.114 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:29:06.120 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:29:06.128 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:29:06.133 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:29:06.167 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:29:06.178 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:29:06.183 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@4c6fe482
19:29:06.189 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:29:06.190 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:29:06.194 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:29:06.203 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730578, negotiated timeout = 60000
19:29:06.205 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:29:06.920 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:29:07.015 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
19:29:07.347 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:29:07.351 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:29:07.498 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 4.259 seconds (JVM running for 4.766)
19:29:07.771 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:29:07.771 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:29:07.772 [RMI TCP Connection(5)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:29:07.772 [RMI TCP Connection(3)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
19:29:07.901 [RMI TCP Connection(5)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:31:06.858 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:31:06.870 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730578 closed
19:31:06.870 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:31:06.905 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:31:06.910 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:31:06.911 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@314a5dcd was destroying!
19:31:06.911 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:31:09.504 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:31:09.506 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:31:09.522 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:31:09.542 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 24071 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:31:09.542 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:31:09.962 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:31:09.963 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:31:09.963 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:31:09.963 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:31:09.963 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:31:10.116 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:31:10.116 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:31:10.116 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:31:10.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:31:10.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:31:10.117 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:31:10.120 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:31:10.148 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:31:10.164 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:31:10.569 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:31:10.574 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:31:10.575 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:31:10.575 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:31:10.620 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:31:10.620 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1049 ms
19:31:11.066 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:31:11.073 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:31:11.079 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:31:11.082 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:31:11.088 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:31:11.097 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:31:11.101 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:31:11.138 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:31:11.151 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:31:11.156 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@734a2c17
19:31:11.163 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:31:11.164 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:31:11.170 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:31:11.177 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730579, negotiated timeout = 60000
19:31:11.179 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:31:11.846 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:31:11.938 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
19:31:12.275 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:31:12.280 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:31:12.422 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.069 seconds (JVM running for 3.655)
19:31:12.579 [RMI TCP Connection(5)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:31:12.579 [RMI TCP Connection(5)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:31:12.579 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:31:12.580 [RMI TCP Connection(5)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
19:31:12.696 [RMI TCP Connection(4)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:31:16.819 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:31:21.221 [http-nio-9090-exec-1] [] WARN  o.s.w.s.m.m.a.ReactiveTypeHandler - 
!!!
Streaming through a reactive type requires an Executor to write to the response.
Please, configure a TaskExecutor in the MVC config under "async support".
The SimpleAsyncTaskExecutor currently in use is not suitable under load.
-------------------------------
Controller:	com.winit.cobra.agent.controller.ChatController
Method:		chatWithStream
Returning:	reactor.core.publisher.Flux<com.winit.cobra.agent.spi.dto.response.ResponseVo<com.winit.cobra.agent.spi.dto.response.ChatVo>>
!!!
19:31:21.561 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:21.563 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193121D243A9103D1197B33254)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:25.078 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:31:27.966 [http-nio-9090-exec-4] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:37.280 [boundedElastic-2] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
java.net.SocketTimeoutException: timeout
	at okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.java:678)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.java:678)
		at okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.java:686)
		at okhttp3.internal.http2.Http2Stream.takeHeaders(Http2Stream.java:154)
		at okhttp3.internal.http2.Http2ExchangeCodec.readResponseHeaders(Http2ExchangeCodec.java:136)
		at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
		at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at com.coze.openapi.service.utils.UserAgentInterceptor.intercept(UserAgentInterceptor.java:25)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at com.coze.openapi.service.service.TimeoutInterceptor.intercept(TimeoutInterceptor.java:39)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at com.coze.openapi.service.service.AuthenticationInterceptor.intercept(AuthenticationInterceptor.java:29)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:37.283 [boundedElastic-2] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: java.net.SocketTimeoutException: timeout
Caused by: java.net.SocketTimeoutException: timeout
	at okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.java:678)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.java:678)
		at okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.java:686)
		at okhttp3.internal.http2.Http2Stream.takeHeaders(Http2Stream.java:154)
		at okhttp3.internal.http2.Http2ExchangeCodec.readResponseHeaders(Http2ExchangeCodec.java:136)
		at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.java:115)
		at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.java:94)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.java:43)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.java:94)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.java:93)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.java:88)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at com.coze.openapi.service.utils.UserAgentInterceptor.intercept(UserAgentInterceptor.java:25)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at com.coze.openapi.service.service.TimeoutInterceptor.intercept(TimeoutInterceptor.java:39)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at com.coze.openapi.service.service.AuthenticationInterceptor.intercept(AuthenticationInterceptor.java:29)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:142)
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.java:117)
		at okhttp3.RealCall.getResponseWithInterceptorChain(RealCall.java:229)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:172)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:39.810 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:31:40.067 [boundedElastic-2] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:40.075 [boundedElastic-2] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=202502281931399E109E94EBC13A573CAF)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:31:43.067 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7475978208098238491","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:31:43.331 [http-nio-9090-exec-8] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:29.695 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:29.975 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:29.976 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193229EFF1BAE2A8238BB93B06)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:35.290 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33333,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:35.530 [http-nio-9090-exec-2] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:36.616 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33333,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:36.818 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:36.822 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=2025022819323670B78DF17B30EEB67413)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:36.827 [http-nio-9090-exec-4] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:37.777 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33333,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:38.031 [http-nio-9090-exec-6] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:40.189 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:40.373 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:40.374 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193240057BFB4FD9DABB5804D6)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:40.440 [http-nio-9090-exec-8] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:42.619 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":3027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:42.913 [boundedElastic-2] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:42.915 [boundedElastic-2] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193242D57677260BF39AB00867)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:48.147 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:48.409 [http-nio-9090-exec-2] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:49.216 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:32:49.392 [http-nio-9090-exec-4] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:49.506 [boundedElastic-2] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:32:49.507 [boundedElastic-2] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=202502281932494E0BC86FC82CBB40705B)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:33:05.410 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:33:05.646 [boundedElastic-3] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:33:05.646 [boundedElastic-3] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=2025022819330571C675B389652A407F9C)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:33:23.811 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:33:24.045 [boundedElastic-2] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:33:24.048 [boundedElastic-2] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193323946405CD362ED73EE5E7)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_        Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_              Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_          Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_               Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_           Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_                  Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_                    Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_              Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_              Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_           Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_        Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_                    Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_                 Flux.replay ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_ ConnectableFlux.autoConnect ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_            Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_            Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:33:35.468 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:33:35.475 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:33:35.475 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e730579 closed
19:33:35.497 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:33:35.501 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:33:35.501 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@43263c42 was destroying!
19:33:35.502 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:33:38.884 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:33:38.886 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:33:38.905 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:33:38.937 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 24582 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:33:38.937 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:33:39.354 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:33:39.354 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:33:39.354 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:33:39.354 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:33:39.354 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:33:39.495 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:39.495 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:33:39.495 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:39.495 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:39.495 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:39.495 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:39.498 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:33:39.524 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:33:39.538 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:33:39.919 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:33:39.925 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:33:39.926 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:33:39.926 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:33:39.976 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:33:39.976 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 997 ms
19:33:40.549 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:33:40.555 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:33:40.561 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:33:40.564 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:33:40.570 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:33:40.577 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:33:40.582 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:33:40.614 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:33:40.625 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:33:40.629 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:33:40.630 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@7699e60a
19:33:40.636 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:33:40.637 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:33:40.641 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:33:40.651 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73057a, negotiated timeout = 60000
19:33:40.653 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:33:41.328 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:33:41.428 [main] [] INFO  org.reflections.Reflections - Reflections took 6 ms to scan 1 urls, producing 2 keys and 2 values 
19:33:41.764 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:33:41.768 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:33:41.923 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.259 seconds (JVM running for 3.891)
19:33:42.370 [RMI TCP Connection(10)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:33:42.373 [RMI TCP Connection(7)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:33:42.373 [RMI TCP Connection(7)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:33:42.374 [RMI TCP Connection(7)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
19:33:42.497 [RMI TCP Connection(10)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:33:44.004 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:33:44.013 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e73057a closed
19:33:44.013 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:33:44.026 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:33:44.028 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:33:44.029 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@7c2500b2 was destroying!
19:33:44.029 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
19:33:47.382 [main] [] INFO  o.a.d.s.b.c.e.WelcomeLogoApplicationListener - 

 :: Dubbo Spring Boot (v2.7.15-SNAPSHOT) : https://github.com/apache/dubbo-spring-boot-project
 :: Dubbo (v2.7.16-SNAPSHOT) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>

19:33:47.383 [main] [] INFO  o.a.d.s.b.c.e.OverrideDubboConfigApplicationListener - Dubbo Config was overridden by externalized configuration {dubbo.application.name=cobra-agent, dubbo.application.organization=winit, dubbo.application.owner=winit, dubbo.application.qos-enable=false, dubbo.config.multiple=true, dubbo.consumer.check=false, dubbo.consumer.ums.version=3.0.0, dubbo.protocol.dispatcher=all, dubbo.protocol.name=dubbo, dubbo.protocol.port=20812, dubbo.protocol.threadpool=limited, dubbo.protocol.threads=200, dubbo.provider.cluster=failover, dubbo.provider.executes=500, dubbo.provider.loadbalance=random, dubbo.provider.retries=2, dubbo.provider.timeout=60000, dubbo.provider.validation=true, dubbo.registry.address=************:2181, dubbo.registry.dynamic=true, dubbo.registry.protocol=zookeeper, dubbo.scan.base-packages=com.winit.gateway.dubbo.swagger.service}
19:33:47.396 [background-preinit] [] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
19:33:47.412 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Starting CobraAgentApplication using Java 17.0.14 on ganyi.local with PID 24626 (/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes started by ganyi in /Users/<USER>/CodeSpace/cobra-agent)
19:33:47.413 [main] [] INFO  c.w.c.agent.CobraAgentApplication - No active profile set, falling back to default profiles: default
19:33:47.778 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
19:33:47.779 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
19:33:47.779 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
19:33:47.779 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
19:33:47.779 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyRegistrationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyRegistrationPostProcessor] has been registered.
19:33:47.921 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:47.921 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
19:33:47.921 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:47.921 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:47.921 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ProviderConfig#0, content : Root bean: class [org.apache.dubbo.config.ProviderConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:47.921 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingRegistrar - The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
19:33:47.924 [main] [] INFO  o.a.d.c.s.b.f.c.DubboConfigEarlyRegistrationPostProcessor - DubboConfigEarlyInitializationPostProcessor has bean registered
19:33:47.963 [main] [] INFO  c.alibaba.spring.util.BeanRegistrar - The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
19:33:47.987 [main] [] INFO  o.s.c.a.ConfigurationClassPostProcessor - Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
19:33:48.437 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
19:33:48.442 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9090"]
19:33:48.443 [main] [] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
19:33:48.443 [main] [] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.98]
19:33:48.491 [main] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
19:33:48.491 [main] [] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1049 ms
19:33:48.969 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:application name="cobra-agent" owner="winit" organization="winit" qosEnable="false" hostname="ganyi.local" />] have been binding by the configuration properties [{name=cobra-agent, owner=winit, organization=winit, qos-enable=false}]
19:33:48.975 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:consumer />] have been binding by the configuration properties [{check=false, ums.version=3.0.0}]
19:33:48.982 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [<dubbo:registry dynamic="true" address="************:2181" port="2181" protocol="zookeeper" />] have been binding by the configuration properties [{protocol=zookeeper, address=************:2181, dynamic=true}]
19:33:48.985 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProtocolConfig{name='dubbo', host='null', port=20812, contextpath='null', threadpool='limited', threadname='null', corethreads=null, threads=200, iothreads=null, alive=null, queues=null, accepts=null, codec='null', serialization='null', charset='null', payload=null, buffer=null, heartbeat=null, accesslog='null', transporter='null', exchanger='null', dispatcher='all', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', register=null, keepAlive=null, optimizer='null', extension='null', parameters=null, isDefault=null, sslEnabled=null}] have been binding by the configuration properties [{name=dubbo, port=20812, dispatcher=all, threadpool=limited, threads=200}]
19:33:48.991 [main] [] INFO  c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor - The configuration bean [ProviderConfig{host='null', port=null, contextpath='null', threadpool='null', threadname='null', threads=null, iothreads=null, alive=null, queues=null, accepts=null, codec='null', charset='null', payload=null, buffer=null, transporter='null', exchanger='null', dispatcher='null', networker='null', server='null', client='null', telnet='null', prompt='null', status='null', wait=null', isDefault=null}] have been binding by the configuration properties [{timeout=60000, validation=true, executes=500, cluster=failover, retries=2, loadbalance=random}]
19:33:48.999 [main] [] INFO  c.w.ubarrier.jmx.agent.UbarrierAgent - The switcher dubbo registered.
19:33:49.003 [main] [] INFO  o.a.d.c.s.b.f.a.ReferenceBeanBuilder - The configBean[type:ReferenceBean] has been built.
19:33:49.037 [main] [] INFO  o.apache.curator.utils.Compatibility - Running in ZooKeeper 3.4.x compatibility mode
19:33:49.052 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Starting
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:zookeeper.version=3.4.6-1569965, built on 02/20/2014 09:09 GMT
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:host.name=localhost
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.version=17.0.14
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.vendor=Eclipse Adoptium
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/temurin-17.0.14/Contents/Home
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.class.path=/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-core/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.4.6/spring-boot-starter-web-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.4.6/spring-boot-starter-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.4.6/spring-boot-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.4.6/spring-boot-starter-logging-2.4.6.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.13.3/log4j-to-slf4j-2.13.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.4.6/spring-boot-starter-json-2.4.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.11.4/jackson-datatype-jdk8-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.11.4/jackson-module-parameter-names-2.11.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.4.6/spring-boot-starter-tomcat-2.4.6.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.98/tomcat-embed-core-9.0.98.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.98/tomcat-embed-websocket-9.0.98.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.7/spring-web-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.7/spring-beans-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.7/spring-webmvc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.7/spring-aop-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.7/spring-expression-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-core/1.0.0-M4/spring-ai-core-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.11.4/jackson-module-jsonSchema-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.11.4/jackson-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.11.4/jackson-core-2.11.4.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.20/swagger-annotations-2.2.20.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.35.0/jsonschema-module-swagger-2-4.35.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.1/antlr4-runtime-4.13.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.6/reactor-core-3.4.6.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.7/spring-context-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.3.7/spring-messaging-5.3.7.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.6.7/micrometer-core-1.6.7.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.1/context-propagation-1.1.1.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.35.0/jsonschema-generator-4.35.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.35.0/jsonschema-module-jackson-4.35.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.11.4/jackson-databind-2.11.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.11.4/jackson-datatype-jsr310-2.11.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.14.2/micrometer-observation-1.14.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.14.2/micrometer-commons-1.14.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.9/mybatis-plus-boot-starter-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.9/mybatis-plus-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.9/mybatis-plus-annotation-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.9/mybatis-plus-extension-3.5.9.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.16/mybatis-3.5.16.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.2/mybatis-spring-2.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.9/mybatis-plus-spring-boot-autoconfigure-3.5.9.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.4.6/spring-boot-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.4.6/spring-boot-starter-jdbc-2.4.6.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.7/spring-jdbc-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.7/spring-tx-5.3.7.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.25/mysql-connector-java-8.0.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/2.4.6/spring-boot-starter-websocket-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.3.7/spring-websocket-5.3.7.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-coze/target/classes:/Users/<USER>/.m2/repository/com/coze/coze-api/0.1.6/coze-api-0.1.6.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.9.0/retrofit-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava2/2.9.0/adapter-rxjava2-2.9.0.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.21/rxjava-2.2.21.jar:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-jackson/2.9.0/converter-jackson-2.9.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.14.9/okhttp-3.14.9.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.17.2/okio-1.17.2.jar:/Users/<USER>/.m2/repository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-core/8.3.1/scribejava-core-8.3.1.jar:/Users/<USER>/.m2/repository/com/github/scribejava/scribejava-java8/8.3.1/scribejava-java8-8.3.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.53/fastjson2-2.0.53.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers-spring-boot-starter/1.0.0-M4/spring-ai-transformers-spring-boot-starter-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-spring-boot-autoconfigure/1.0.0-M4/spring-ai-spring-boot-autoconfigure-1.0.0-M4.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-transformers/1.0.0-M4/spring-ai-transformers-1.0.0-M4.jar:/Users/<USER>/.m2/repository/com/microsoft/onnxruntime/onnxruntime/1.19.2/onnxruntime-1.19.2.jar:/Users/<USER>/.m2/repository/ai/djl/pytorch/pytorch-engine/0.30.0/pytorch-engine-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/api/0.30.0/api-0.30.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/ai/djl/model-zoo/0.30.0/model-zoo-0.30.0.jar:/Users/<USER>/.m2/repository/ai/djl/huggingface/tokenizers/0.30.0/tokenizers-0.30.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.26/hutool-core-5.8.26.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.26/hutool-crypto-5.8.26.jar:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-adapter/3.4.3/reactor-adapter-3.4.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.18.1/assertj-core-3.18.1.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.22/byte-buddy-1.10.22.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.7/spring-core-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.7/spring-jcl-5.3.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.4.6/spring-boot-starter-validation-2.4.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.1.7.Final/hibernate-validator-6.1.7.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes:/Users/<USER>/.m2/repository/com/winit/winit-commons/3.1.0-SNAPSHOT/winit-commons-3.1.0-20211221.064333-9.jar:/Users/<USER>/.m2/repository/com/winit/winit-config-loader/1.0.0.RELEASE/winit-config-loader-1.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.11/swagger-annotations-1.6.11.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.11.4/jackson-dataformat-yaml-2.11.4.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/com/winit/spi-utils/1.3.1-RELEASE/spi-utils-1.3.1-RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.1.3.Final/hibernate-validator-5.1.3.Final.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.11.4/jackson-module-jaxb-annotations-2.11.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.13/fluent-hc-4.5.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.7/spring-context-support-5.3.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.9.6/aspectjrt-1.9.6.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.6/aspectjweaver-1.9.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.11.4/jackson-dataformat-xml-2.11.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.3/woodstox-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.8.3/jackson-jaxrs-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.8.3/jackson-core-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.8.3/jackson-mapper-asl-1.8.3.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.33/fastjson-2.0.33.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.33/fastjson2-extension-2.0.33.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/com/winit/spi-ums/1.2.197-SNAPSHOT/spi-ums-1.2.197-20250104.044807-2.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.16-SNAPSHOT/dubbo-2.7.16-20231018.092330-9-winit01.jar:/Users/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.23.1-GA/javassist-3.23.1-GA.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.65.Final/netty-all-4.1.65.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.27/snakeyaml-1.27.jar:/Users/<USER>/.m2/repository/com/winit/ubarrier/ubarrier-jmx-agent/1.2.0-SNAPSHOT/ubarrier-jmx-agent-1.2.0-20231102.105912-5.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.0.0/curator-recipes-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.0.0/curator-framework-4.0.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.0.0/curator-client-4.0.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.16/log4j-1.2.16.jar:/Users/<USER>/.m2/repository/jline/jline/0.9.94/jline-0.9.94.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.7.0.Final/netty-3.7.0.Final.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-starter/2.7.15-SNAPSHOT/dubbo-spring-boot-starter-2.7.15-20220217.040250-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-2.7.15-20220217.040245-2.jar:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-spring-boot-autoconfigure-compatible/2.7.15-SNAPSHOT/dubbo-spring-boot-autoconfigure-compatible-2.7.15-20220217.040240-2.jar:/Users/<USER>/.m2/repository/com/winit/gateway-apache-dubbo/1.2.0-SNAPSHOT/gateway-apache-dubbo-1.2.0-20210927.032748-22.jar:/Users/<USER>/.m2/repository/com/winit/gateway-common/1.2.0-SNAPSHOT/gateway-common-1.2.0-20210927.032712-22.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-core/1.6.2/swagger-core-1.6.2.jar:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.6.2/swagger-models-1.6.2.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.8.5/swagger-bootstrap-ui-1.8.5.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-boot-starter/3.0.0/springfox-boot-starter-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-oas/3.0.0/springfox-oas-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/3.0.0/springfox-spi-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/3.0.0/springfox-schema-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-core/3.0.0/springfox-core-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.83/classgraph-4.8.83.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webflux/3.0.0/springfox-spring-webflux-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/3.0.0/springfox-swagger-common-3.0.0.jar:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-data-rest/3.0.0/springfox-data-rest-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-bean-validators/3.0.0/springfox-bean-validators-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/3.0.0/springfox-swagger2-3.0.0.jar:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/3.0.0/springfox-swagger-ui-3.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.4.6/spring-boot-starter-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.4.6/spring-boot-actuator-autoconfigure-2.4.6.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.4.6/spring-boot-actuator-2.4.6.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.6.7/micrometer-registry-prometheus-1.6.7.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.9.0/simpleclient_common-0.9.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.9.0/simpleclient-0.9.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/24.0.0/annotations-24.0.0.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.io.tmpdir=/var/folders/mx/bkshg6w937j5fv4h_4l21qsh0000gn/T/
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:java.compiler=<NA>
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.name=Mac OS X
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.arch=aarch64
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:os.version=15.2
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.name=ganyi
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.home=/Users/<USER>
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Client environment:user.dir=/Users/<USER>/CodeSpace/cobra-agent
19:33:49.056 [main] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@2fd05b15
19:33:49.063 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
19:33:49.063 [main] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - Default schema
19:33:49.069 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
19:33:49.077 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73057b, negotiated timeout = 60000
19:33:49.080 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: CONNECTED
19:33:49.729 [main] [] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/cobra-agent'
19:33:49.829 [main] [] INFO  org.reflections.Reflections - Reflections took 5 ms to scan 1 urls, producing 2 keys and 2 values 
19:33:50.154 [main] [] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9090"]
19:33:50.158 [main] [] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
19:33:50.299 [main] [] INFO  c.w.c.agent.CobraAgentApplication - Started CobraAgentApplication in 3.053 seconds (JVM running for 3.543)
19:33:50.525 [RMI TCP Connection(7)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
19:33:50.525 [RMI TCP Connection(6)-127.0.0.1] [] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:33:50.525 [RMI TCP Connection(6)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
19:33:50.527 [RMI TCP Connection(6)-127.0.0.1] [] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
19:33:50.641 [RMI TCP Connection(7)-127.0.0.1] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
19:33:54.345 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:33:54.464 [http-nio-9090-exec-1] [] WARN  o.s.w.s.m.m.a.ReactiveTypeHandler - 
!!!
Streaming through a reactive type requires an Executor to write to the response.
Please, configure a TaskExecutor in the MVC config under "async support".
The SimpleAsyncTaskExecutor currently in use is not suitable under load.
-------------------------------
Controller:	com.winit.cobra.agent.controller.ChatController
Method:		chatWithStream
Returning:	reactor.core.publisher.Flux<com.winit.cobra.agent.spi.dto.response.ResponseVo<com.winit.cobra.agent.spi.dto.response.ChatVo>>
!!!
19:33:54.734 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:33:54.735 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=2025022819335494BCBDA02E2A115832FB)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_     Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_     Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:34:03.150 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:34:49.867 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:34:50.106 [http-nio-9090-exec-6] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:05.020 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:05.259 [http-nio-9090-exec-8] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:07.305 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:07.527 [http-nio-9090-exec-10] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:08.898 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:09.092 [http-nio-9090-exec-2] [] ERROR c.w.c.a.a.ExceptionHandlerAdvice - {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:09.098 [boundedElastic-3] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:09.101 [boundedElastic-3] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=202502281935087FDB4A423301FBB80C2C)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_     Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_     Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:10.546 [http-nio-9090-exec-3] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:10.756 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:10.759 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=202502281935108C5C811F3D881B5A5D38)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_     Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_     Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:30.347 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12dd"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:30.642 [boundedElastic-3] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:30.645 [boundedElastic-3] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=2025022819353001BA2919D4AF37BADF15)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_     Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_     Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:42.823 [http-nio-9090-exec-7] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12dd"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:43.072 [boundedElastic-1] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:43.072 [boundedElastic-1] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193542EA61C77F8C6AFD62E98A)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_     Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_     Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:53.646 [http-nio-9090-exec-9] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"<EMAIL>","client_id":"40c5d18a8f38c5b28e","client_sign":"A1D2A7EF72155B19360671B38F587199","data":{"chatId":"","conversationId":"7476367866644824079","userId":33027,"username":"<EMAIL>","message":"12dd"},"format":"json","language":"zh_CN","platform":"coze","sign":"A1D2A7EF72155B19360671B38F587199","sign_method":"md5","timestamp":"1740741134328","version":"1.0"}
19:35:53.903 [boundedElastic-3] [] ERROR o.s.ai.chat.model.MessageAggregator - Aggregation Error
com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:35:53.904 [boundedElastic-3] [] ERROR reactor.core.publisher.Operators - Operator called default onErrorDropped
reactor.core.Exceptions$ErrorCallbackNotImplemented: CozeApiExcetion(code=0, msg={"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}, logID=20250228193553D00E253F029A13BDADD1)
Caused by: com.coze.openapi.client.exception.CozeApiExcetion: {"code":4016,"msg":"Conversation occupied: Another chat is currently active. Please wait for the ongoing chat to complete or create a new conversation to start a separate chat. Ensure only one chat runs at a time within the same conversation."}
	at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxDeferContextual] :
	reactor.core.publisher.Flux.deferContextual(Flux.java:843)
	com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
Error has been observed at the following site(s):
	|_ Flux.deferContextual ⇢ at com.winit.cobra.agent.coze.model.CozeChatModel.stream(CozeChatModel.java:129)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:694)
	|_       Flux.publishOn ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$2.aroundStream(DefaultChatClient.java:695)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:55)
	|_   Flux.doOnSubscribe ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:93)
	|_        Flux.doOnNext ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:104)
	|_    Flux.doOnComplete ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:143)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregate(MessageAggregator.java:170)
	|_             Flux.map ⇢ at org.springframework.ai.chat.model.MessageAggregator.aggregateAdvisedResponse(MessageAggregator.java:68)
	|_           Flux.defer ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:124)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:125)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:126)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundStream$6(DefaultAroundAdvisorChain.java:127)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundStream(DefaultAroundAdvisorChain.java:103)
	|_             Flux.map ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:548)
	|_       Flux.doOnError ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:549)
	|_       Flux.doFinally ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:550)
	|_    Flux.contextWrite ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.lambda$doGetObservableFluxChatResponse$3(DefaultChatClient.java:551)
	|_ Flux.deferContextual ⇢ at org.springframework.ai.chat.client.DefaultChatClient$DefaultStreamResponseSpec.doGetObservableFluxChatResponse(DefaultChatClient.java:527)
	|_             Flux.map ⇢ at com.winit.cobra.agent.service.ChatServiceImpl.streamChat(ChatServiceImpl.java:82)
	|_             Flux.map ⇢ at com.winit.cobra.agent.controller.ChatController.chatWithStream(ChatController.java:61)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_           Flux.cache ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:67)
	|_     Flux.collectList ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:68)
	|_     Mono.doOnSuccess ⇢ at com.winit.cobra.agent.aspect.log.ApiRequestLogAspect.logApi(ApiRequestLogAspect.java:69)
Stack trace:
		at com.coze.openapi.client.chat.model.ChatEvent.parseEvent(ChatEvent.java:39)
		at com.coze.openapi.service.service.chat.EventCallback.processLine(EventCallback.java:31)
		at com.coze.openapi.service.service.common.AbstractEventCallback.onResponse(AbstractEventCallback.java:64)
		at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
		at okhttp3.RealCall$AsyncCall.execute(RealCall.java:174)
		at okhttp3.internal.NamedRunnable.run(NamedRunnable.java:32)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:840)
19:36:07.684 [Curator-Framework-0] [] INFO  o.a.c.f.imps.CuratorFrameworkImpl - backgroundOperationsLoop exiting
19:36:07.697 [SpringContextShutdownHook] [] INFO  org.apache.zookeeper.ZooKeeper - Session: 0x19439960e73057b closed
19:36:07.697 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
19:36:07.721 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
19:36:07.732 [SpringContextShutdownHook] [] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
19:36:07.734 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - org.apache.dubbo.common.bytecode.proxy0@20409580 was destroying!
19:36:07.734 [SpringContextShutdownHook] [] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!
