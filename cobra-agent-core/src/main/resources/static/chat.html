<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .chat-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 80vh;
        }

        #chat-box {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            position: relative;
            animation: fadeIn 0.3s ease;
        }

        .user-message {
            background-color: #007AFF;
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }

        .bot-message {
            background-color: #f0f0f0;
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        #user-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        #user-input:focus {
            border-color: #007AFF;
        }

        #send-btn {
            padding: 12px 24px;
            background-color: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        #send-btn:hover {
            background-color: #0056b3;
        }

        #send-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background-color: #f0f0f0;
            border-radius: 12px;
            align-self: flex-start;
            margin-bottom: 16px;
        }

        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #90949c;
            border-radius: 50%;
            margin-right: 4px;
            animation: typing 1s infinite;
        }

        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 600px) {
            .container {
                margin: 0;
                padding: 10px;
            }
            
            .chat-container {
                height: 100vh;
            }
            
            .message {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI 对话助手</h1>
        </div>
        <div class="chat-container">
            <div id="chat-box"></div>
            <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="input-container">
                <input type="text" id="user-input" placeholder="输入消息..." autocomplete="off">
                <button id="send-btn">发送</button>
            </div>
        </div>
    </div>

    <script>
        const chatBox = document.getElementById('chat-box');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const typingIndicator = document.querySelector('.typing-indicator');

        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
            messageDiv.textContent = content;
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function setTypingIndicator(show) {
            typingIndicator.style.display = show ? 'block' : 'none';
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            // 禁用输入和发送按钮
            userInput.disabled = true;
            sendBtn.disabled = true;

            // 显示用户消息
            addMessage(message, true);
            userInput.value = '';

            // 显示输入指示器
            setTypingIndicator(true);

            try {
                const response = await fetch('http://127.0.0.1:9090/service', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        "action": "v1.chatWithStream",
                        "app_key": "rebecca",
                        "client_id": "40c5d18a8f38c5b28e",
                        "client_sign": "42FEA2285EB887E1B6E1848ECEC42D3B",
                        "data": {
                            "chatId": "",
                            "conversationId": "7483115971058712576",
                            "message": message,
                            "userId": 3109,
                            "username": "rebecca"
                        },
                        "format": "json",
                        "language": "zh_CN",
                        "platform": "coze",
                        "sign": "42FEA2285EB887E1B6E1848ECEC42D3B",
                        "sign_method": "md5",
                        "timestamp": Date.now().toString(),
                        "version": "1.0"
                    })
                });

                if (!response.body) return;

                // 创建机器人消息容器
                const botMessageDiv = document.createElement('div');
                botMessageDiv.className = 'message bot-message';
                chatBox.appendChild(botMessageDiv);

                const reader = response.body.getReader();
                const decoder = new TextDecoder("utf-8");
                let botResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const text = decoder.decode(value, { stream: true });
                    text.split('data:').forEach(chunk => {
                        if (chunk.trim()) {
                            try {
                                const json = JSON.parse(chunk.trim());
                               console.log(json)
                            } catch (e) {
                                console.error("解析错误", e);
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('抱歉，发送消息时出现错误。');
            } finally {
                // 隐藏输入指示器
                setTypingIndicator(false);
                // 重新启用输入和发送按钮
                userInput.disabled = false;
                sendBtn.disabled = false;
                userInput.focus();
            }
        }

        // 事件监听器
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载时聚焦输入框
        userInput.focus();
    </script>
</body>
</html>