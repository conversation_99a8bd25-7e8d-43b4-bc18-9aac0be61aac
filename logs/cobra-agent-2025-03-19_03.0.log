03:07:45.617 [DubboMetadataReportTimer-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DU<PERSON><PERSON>] start to publish all metadata., dubbo version: 2.7.16-SNAPSHOT, current host: ***********
03:07:45.623 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@4c49c579; definition: {owner=winit, init=false, side=consumer, release=2.7.16-SNAPSHOT, methods=resetPassword,getLoginCredentialsUserInfo,modifyPassword,getUser,querySigleChildAccount,refreshTokenLatestLog,queryAllByCompanyCode,updateLockAccountInfo,getAccountValidateImage,modifyUserPasswordByVerificationCode,isExistsPhone,updateAccountValidateInfo,printPassword,queryByUserId,bindUserLoginCredentials,loginCheck,queryAllAccountByCode,validateLoginPassword,batchResetPassword,resetToken,queryByUsername,checkEmail,checkAccountValidateInfo,queryChildListByChannelCode,queryLoginCredentialsList,queryUserByAdUserId,queryChildAccountBySeller,addChildAccount,updateChildAccount,applyForChangePassword,queryAllChildAccountByCode,unBindUserLoginCredentials,isExistsEmail,getUserToken,updateUserFerquencyByUserName,getToken,queryAccountByCompanyIdAndType,updateLastLoginTime,queryChildAccountList,queryByCompanyCode,refreshToken, dubbo=2.0.2, check=false, interface=com.winit.ums.spi.UmsUserService, version=3.0.0, qos.enable=false, revision=1.2.197-SNAPSHOT, protocol=dubbo, metadata-type=remote, application=cobra-agent, organization=winit, sticky=false}, dubbo version: 2.7.16-SNAPSHOT, current host: ***********
03:07:45.646 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@444ffa17; definition: FullServiceDefinition{parameters={cluster=failover, server=wservlet, release=2.7.16-SNAPSHOT, methods=startConversation,createConversation,listConversations, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ConversationService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ConversationService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=startConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StartConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.StartConversationVo], MethodDefinition [name=createConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.CreateConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.CreateConversationVo], MethodDefinition [name=listConversations, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageConversationsVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
03:07:45.669 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@22a46f4d; definition: FullServiceDefinition{parameters={cluster=failover, release=2.7.16-SNAPSHOT, methods=startConversation,createConversation,listConversations, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ConversationService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ConversationService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ConversationService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=startConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StartConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.StartConversationVo], MethodDefinition [name=createConversation, parameterTypes=[com.winit.cobra.agent.spi.dto.request.CreateConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.CreateConversationVo], MethodDefinition [name=listConversations, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListConversationCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageConversationsVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
03:07:45.690 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@8a344c8; definition: FullServiceDefinition{parameters={cluster=failover, server=wservlet, release=2.7.16-SNAPSHOT, methods=transferToHuman,chat,streamChat,listChat, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ChatService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ChatService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=chat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.ChatVo], MethodDefinition [name=streamChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StreamChatCommand], returnType=reactor.core.publisher.Flux], MethodDefinition [name=listChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo], MethodDefinition [name=transferToHuman, parameterTypes=[com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand], returnType=com.winit.cobra.agent.spi.dto.response.TransferToHumanVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
03:07:45.710 [DubboSaveMetadataReport-thread-1] [] INFO  o.a.d.m.s.z.ZookeeperMetadataReport -  [DUBBO] store provider metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@3e6ab549; definition: FullServiceDefinition{parameters={cluster=failover, release=2.7.16-SNAPSHOT, methods=transferToHuman,chat,streamChat,listChat, deprecated=false, dubbo=2.0.2, loadbalance=random, interface=com.winit.cobra.agent.spi.service.ChatService, threadpool=limited, qos.enable=false, timeout=60000, dynamic=true, executes=500, dispatcher=all, validation=true, anyhost=true, owner=winit, side=provider, service.name=ServiceBean:/com.winit.cobra.agent.spi.service.ChatService:3.0.0, threads=200, version=3.0.0, generic=false, revision=3.0.0, retries=2, metadata-type=remote, application=cobra-agent, organization=winit}} ServiceDefinition [canonicalName=com.winit.cobra.agent.spi.service.ChatService, codeSource=file:/Users/<USER>/CodeSpace/cobra-agent/cobra-agent-spi/target/classes/, methods=[MethodDefinition [name=chat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.ChatVo], MethodDefinition [name=streamChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.StreamChatCommand], returnType=reactor.core.publisher.Flux], MethodDefinition [name=listChat, parameterTypes=[com.winit.cobra.agent.spi.dto.request.ListChatCommand], returnType=com.winit.cobra.agent.spi.dto.response.PageChatMessagesVo], MethodDefinition [name=transferToHuman, parameterTypes=[com.winit.cobra.agent.spi.dto.request.TransferToHumanCommand], returnType=com.winit.cobra.agent.spi.dto.response.TransferToHumanVo]]], dubbo version: 2.7.16-SNAPSHOT, current host: ***********
