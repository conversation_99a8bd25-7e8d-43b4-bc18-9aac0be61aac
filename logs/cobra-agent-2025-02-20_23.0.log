23:55:07.232 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 973645ms for sessionid 0x19439960e730491, closing socket connection and attempting reconnect
23:55:07.341 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
23:55:08.409 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
23:55:08.413 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
23:55:08.418 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
23:55:08.419 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
23:55:08.418 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730491 has expired, closing socket connection
23:55:08.422 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
23:55:08.422 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
23:55:08.422 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
23:55:08.427 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
23:55:08.439 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730492, negotiated timeout = 60000
23:55:08.439 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
23:55:16.459 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m23s664ms).
