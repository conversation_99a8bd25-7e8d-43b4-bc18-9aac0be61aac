20:02:59.651 [http-nio-9090-exec-5] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhang<PERSON>","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
20:02:59.661 [http-nio-9090-exec-5] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
20:09:20.189 [http-nio-9090-exec-8] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
20:09:20.193 [http-nio-9090-exec-8] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
20:12:44.748 [http-nio-9090-exec-1] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
20:12:44.765 [http-nio-9090-exec-1] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
20:14:05.025 [http-nio-9090-exec-4] [] INFO  c.w.c.a.c.DispatchServiceController - 收到api请求，参数={"action":"v1.chatWithStream","app_key":"yang.ou","client_id":"40c5d18a8f38c5b28e","client_sign":"23DB5784D39232ACFFAD5BBDBE4E6CA3B6A00099","data":{"message":"query_money","username":"zhangsan","userId":"chengcheng.qin","conversationId":"","chatId":"","toolContext":{"in":"test"},"toolCallOut":[],"customVariables":{"user_id":"123","username":"1233","conversation_id":"conversationId"}},"format":"json","language":"zh_CN","platform":"iam","sign":"4B01A79E1BF7B44A07C308947DF9F5EB2F815750","sign_method":"HmacSha1","timestamp":"2025-02-18T08:49:08.263Z","version":"1.0"}
20:14:05.033 [http-nio-9090-exec-4] [] INFO  c.w.c.agent.service.ChatServiceImpl - conversationId is null,a new conversation start, create conversation for user:chengcheng.qin
20:37:55.492 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 756633ms for sessionid 0x19439960e730470, closing socket connection and attempting reconnect
20:37:55.598 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
20:37:56.739 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
20:37:56.745 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
20:37:56.749 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
20:37:56.750 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
20:37:56.750 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730470 has expired, closing socket connection
20:37:56.757 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
20:37:56.758 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
20:37:56.764 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
20:37:56.770 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
20:37:56.778 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73047c, negotiated timeout = 60000
20:37:56.780 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
20:38:11.618 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m46s651ms).
20:55:14.287 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 1010991ms for sessionid 0x19439960e73047c, closing socket connection and attempting reconnect
20:55:14.396 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
20:55:15.499 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
20:55:15.505 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
20:55:15.514 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
20:55:15.514 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e73047c has expired, closing socket connection
20:55:15.514 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
20:55:15.515 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
20:55:15.515 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
20:55:15.516 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
20:55:15.520 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
20:55:15.526 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e730482, negotiated timeout = 60000
20:55:15.527 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
20:55:42.620 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m1s).
