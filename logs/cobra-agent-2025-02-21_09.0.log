09:57:44.764 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 54628ms for sessionid 0x19439960e7304a0, closing socket connection and attempting reconnect
09:57:44.880 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
09:57:45.564 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m4s645ms).
09:57:46.777 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
09:57:46.784 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
09:57:46.789 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e7304a0, negotiated timeout = 60000
09:57:46.789 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
09:59:52.443 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 55722ms for sessionid 0x19439960e7304a0, closing socket connection and attempting reconnect
09:59:52.546 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
09:59:53.632 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
09:59:53.639 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
09:59:53.644 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e7304a0, negotiated timeout = 60000
09:59:53.644 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
09:59:56.060 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m5s733ms).
