21:17:05.851 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Client session timed out, have not heard from server in 1021538ms for sessionid 0x19439960e730482, closing socket connection and attempting reconnect
21:17:05.960 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: SUSPENDED
21:17:07.791 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
21:17:07.797 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
21:17:07.802 [main-EventThread] [] WARN  org.apache.curator.ConnectionState - Session expired event received
21:17:07.802 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Unable to reconnect to ZooKeeper service, session 0x19439960e730482 has expired, closing socket connection
21:17:07.802 [main-EventThread] [] INFO  org.apache.zookeeper.ZooKeeper - Initiating client connection, connectString=************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@69a3944
21:17:07.804 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Opening socket connection to server ************/************:2181. Will not attempt to authenticate using SASL (unknown error)
21:17:07.804 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: LOST
21:17:07.804 [main-EventThread] [] INFO  org.apache.zookeeper.ClientCnxn - EventThread shut down
21:17:07.808 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Socket connection established to ************/************:2181, initiating session
21:17:07.817 [main-SendThread(************:2181)] [] INFO  org.apache.zookeeper.ClientCnxn - Session establishment complete on server ************/************:2181, sessionid = 0x19439960e73048b, negotiated timeout = 60000
21:17:07.817 [main-EventThread] [] INFO  o.a.c.f.state.ConnectionStateManager - State change: RECONNECTED
21:17:24.194 [HikariPool-1 housekeeper] [] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m11s538ms).
